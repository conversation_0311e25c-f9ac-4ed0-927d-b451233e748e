import request from '@/utils/request'

//获取上架单
export function getShelfList(data) {
    return request({
        url: '/instock/purchase/storageOrder/findList',
        method: 'post',
        data: data
    })
}

//商品上架明细列表
export function getShelfDetailList(data) {
    return request({
        url: '/instock/purchase/storageOrder/findOrderDetailList',
        method: 'post',
        data: data
    })
}

//联想查询上架区域/入库单编号
export function getShelfAreaList(data) {
    return request({
        url: '/instock/purchase/storageOrder/associativeStorageSearchList',
        method: 'post',
        data: data
    })
}

//模糊查询收货单
export function getReceiptList(data) {
    return request({
        url: '/instock/receiveOrder/getList4VagueCode',
        method: 'post',
        data: data
    })
}

/**
 *  获取商品选择列表
 * @param {object} 查询实体
 */
export function getProductList(data) {
    return request({
    url: '/instock/common/selectListProduct',
    method: 'post',
    data: data
    })
}

//验收管理-查询外部用户（采购员）
export function getBuyerList(data) {
    return request({
        url: "/basicdata/system/outUser/byName",
        method: "post",
        data: data
    })
}

/**
 * 获取供应商信息
 * @param {data} 请求体
 */
export function getListSupplier(data) {
    return request({
    url: '/instock/common/selectListSupplier',
    method: 'post',
    data
    })
}

//验收管理-查询收货员
export function getReceiverList(data) {
    return request({
        url: "/basicdata/system/user/pageByPost",
        method: "post",
        data: data
    })
}

//查询超时上架单数量及条目数
export function getShelfOverTimeList(data) {
    return request({
        url: '/instock/purchase/storageOrder/getStorageOrderOvertimeCount',
        method: 'post',
        data: data
    })
}

//查询待上架单数量及条目数
export function getShelfWaitList(data) {
    return request({
        url: '/instock/purchase/storageOrder/getStorageOrderWaitCount',
        method: 'post',
        data: data
    })
}

//入库单明细导出
export function exportShelfDetailList(data) {
    return request({
        url: '/export/submitExportTask',
        method: 'post',
        data: data
    })
}