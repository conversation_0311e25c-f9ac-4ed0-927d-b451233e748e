import request from "@/utils/request";

//保存库容补货
export function findStock(data) {
  return request({
    url: "/warehouse/stock/findGoodsAllocationProducts",
    method: "post",
    data: data,
  });
}

//业务查询
export function findChannelCode(data) {
  return request({
    url: "/basicdata/ownerChannel/getLikeOwnerChannel",
    method: "post",
    data: data,
  });
}


//商品查询
export function findGoodsAllocationProducts(data) {
  return request({
    url: "/basicdata/master/productBase/page",
    method: "post",
    data: data,
  });
}


