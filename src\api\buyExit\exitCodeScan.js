import request from "@/utils/request"

// 追溯码扫描列表查询
export function buyExitCodeScanList(data) {
  return request(
    {
      url: '/outstock/web/purchase/scan/queryPurchaseCodeScanOrder',
      method: 'post',
      data
    }
  )
}
// 追溯码扫描查询列表明细
export function buyExitCodeScanDetailList(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/queryPerformDocumentDetail',
      method: 'post',
      data
    }
  )
}
// 追溯码扫描查询未扫描明细
export function buyExitGetNoScanList(data) {
  return request(
    {
      url: '/outstock/web/purchase/scan/queryUnScannedItem',
      method: 'post',
      params: data
    }
  )
}
// 扫描追溯码
export function exitCodeScanSubmit(data) {
  return request({
    url: '/outstock/web/purchase/scan/scanCode',
    method: 'post',
    data
  })
}
// 扫描追溯码操作详情
export function exitCodeScanSubmitDetail(data) {
  return request({
    url: '/outstock/web/outstock/whole/picking/getScanCompleteData',
    method: 'get',
    params: data
  })
}
// 删除扫描追溯码操作详情
export function exitCodeScanDelete(data) {
  return request({
    url: '/outstock/web/outstock/whole/picking/deleteScanCode',
    method: 'post',
    params: data
  })
}
/**
 * 追溯码解锁
 */
export function unlockThisTask(data){
  return request({
    url: "/outstock/web/purchase/scan/toUnlockThisTask",
    method: "post",
    data
  })
}