import request from "@/utils/request";

//查询补货列表
export function findStatistics(data) {
  return request({
    url: "/warehouse/replenish/statistics",
    method: "post",
    data: data,
  });
}

//查询标签编码
export function findReplenishUpList(data) {
  return request({
    url: "warehouse/replenish/findReplenishUpList",
    method: "post",
    data: data,
  });
}

//根据工号查询姓名
export function findEmpNumber(data) {
  return request({
    url: "warehouse/replenish/findEmpNumber",
    method: "post",
    data: data,
  });
}

//查询补货任务信息
export function askUpTask(data) {
  return request({
    url: "warehouse/replenish/askUpTask",
    method: "post",
    data: data,
  });
}

//上架确认
export function confirmUp(data) {
  return request({
    url: "warehouse/replenish/up",
    method: "post",
    data: data,
  });
}

//上架确认
export function getDownTask(data) {
  return request({
    url: "warehouse/replenish/getDownTask",
    method: "post",
    data: data,
  });
}

//重打补货标签
export function rePrintTag(data) {
  return request({
    url: "warehouse/replenish/rePrintTag",
    method: "post",
    data: data,
  });
}
