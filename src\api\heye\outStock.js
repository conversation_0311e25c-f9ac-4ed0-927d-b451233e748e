import request from '@/utils/request'
/**
 * 列表 权益卡-出库管理-拣货列表
 * @param {Object} data
 */
export function apiGetReciveList(params) {
  return request({
      url: '/equityCard/salesOrder/list'+params,
      method: 'get',
      data: {}
  })
}
/**
 * 列表 权益卡-出库管理-拣货员列表
 * @param {Object} data
 */
export function apiGetpickingUser(params) {
    return request({
        url: '/equityCard/salesOrder/pickingUser',
        method: 'get',
        data: params
    })
}
/**
 * 权益卡-下发订单/打印拣货单、面单
 * @param {Object} data
 */
export function apiSalesOrderIssue(params) {
    return request({
        url: '/equityCard/salesOrder/issue',
        method: 'post',
        data: params
    })
}
/**
 * 列表 权益卡-出库管理-索取拣货单号
 * @param {Object} data
 */
export function apiGetReviewAskFor(params) {
    return request({
        url: '/equityCard/salesOrder/review/askFor'+params,
        method: 'post',
        data: {}
    })
}
/**
 * 列表 权益卡-出库管理-复核列表
 * @param {Object} data
 */
export function apiGetReviewList(params) {
    return request({
        url: '/equityCard/salesOrder/review/detailList'+params,
        method: 'get',
        data: {}
    })
}
/**
 * 列表 权益卡-出库管理-单条复核操作
 * @param {Object} data
 */
export function apiSignleReview(params) {
    return request({
        url: '/equityCard/salesOrder/review/review',
        method: 'post',
        data: params
    })
}
/**
 * 列表 权益卡-出库管理-复核完成
 * @param {Object} data
 */
export function apiFinishReview(params) {
    return request({
        url: '/equityCard/salesOrder/review/confirm'+params,
        method: 'post',
        data: {}
    })
}
/** 
 * 列表 权益卡-出库管理-增加拼箱
 * @param {Object} data
*/
export function apiAddBoxReview(params) {
    return request({
        url: '/equityCard/salesOrder/review/add-box'+params,
        method: 'get',
    })
}
/** 
 * 列表 权益卡-出库管理-增加拼箱
 * @param {Object} data
*/
export function apiViewTasks(params) {
    return request({
        url: '/equityCard/salesOrder/review/viewTasksPage',
        method: 'post',
        data: params
    })
}