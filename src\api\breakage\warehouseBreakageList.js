import request from "@/utils/request";

//获取字典枚举值
export function getDictCode(query) {
    return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
    });
}

//获取报损单列表
export function getBreakageList(data) {
    return request({
    url: "/warehouse/breakage/pageQuery",
    method: "post",
    data: data,
    });
}

//不合格品报损单详情
export function getBreakageDetailList(data) {
    return request({
    url: "/warehouse/warehouseCheck/overflowDetailQuery",
    method: "post",
    data: data,
    });
}
