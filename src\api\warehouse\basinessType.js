import request from '@/utils/request'

/**
 *  业务类型申请单
 * @param {object} 查询实体
 */
export function apiApplyQueryList(params) {
  return request({
    url: 'warehouse/wmsApp/channelMoveExecute/getChannelMoveList',
    method: 'post',
    data: params
  })
}
/**
 *  业务类型申请单-详情
 * @param {object} 查询实体
 */
export function getBussinessTypeAdjustApplyOrderDetail(params) {
  return request({
    url: 'warehouse/wmsApp/channelMoveExecute/getChannelMoveDetails',
    method: 'post',
    data: params
  })
}
/**
 *  业务类型执行单
 * @param {object} 查询实体
 */
export function apiQueryList(params) {
    return request({
      url: 'warehouse/wmsApp/channelMoveExecute/getChannelMoveExecList',
      method: 'post',
      data: params
    })
}

/**
 *  业务类型执行单详情
 * @param {object} 查询实体
 */
export function apiBussinessTypeAdjustExecuteOrderDetail(params) {
  return request({
    url: 'warehouse/wmsApp/channelMoveExecute/getChannelMoveExecDetails',
    method: 'post',
    data: params
  })
}

/**
 *  业务类型调整申请单详情
 * @param {object} 查询实体
 */
export function apiBussinessTypeAdjustOrderDetailIntersection(params) {
  return request({
    url: 'warehouse/wmsApp/channelMoveExecute/channelCommon',
    method: 'post',
    data: params
  })
}

