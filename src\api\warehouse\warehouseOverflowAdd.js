import request from "@/utils/request";

//获取字典字段
export function getDictCode(query) {
  return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
  });
}

//盘盈商品列表查询
export function getOverflowList(data) {
  return request({
    url: "/warehouse/warehouseCheck/selectSurplusGoods",
    method: "post",
    data: data,
  });
}

//修改损益原因
export function updateOverflowReason(data) {
  return request({
    url: "/warehouse/warehouseCheck/updateBillReason",
    method: "post",
    data: data,
  });
}

//盘损商品列表查询
export function getLossList(data) {
  return request({
    url: "/warehouse/warehouseCheck/selectLossGoods",
    method: "post",
    data: data,
  });
}

//删除盘点数据
export function deleteOverflow(data) {
  return request({
    url: "/warehouse/warehouseCheck/deleteBillWait",
    method: "post",
    data: data,
  });
}

//损益结果试算
export function getOverflowTrial(data) {
  return request({
    url: "/warehouse/warehouseCheck/trialOverflow",
    method: "post",
    data: data,
  });
}

//损益单提交
export function submitOverflow(data) {
  return request({
    url: "/warehouse/warehouseCheck/saveOverflowing",
    method: "post",
    data: data,
  });
}

//添加盘损商品
export function addLossGoods(data) {
  return request({
    url: "/warehouse/warehouseCheck/addLossGoods",
    method: "post",
    data: data,
  });
}

//损益单号获取
export function getOverflowNo() {
  return request({
    url: "/warehouse/warehouseCheck/applyOverflowNo",
    method: "post",
    data: {},
  });
}

//导入盘盈商品
export function importOverflow(data) {
  return request({
    url: "/warehouse/warehouseCheck/importInventorySurplus",
    method: "post",
    data: data,
  });
}