import request from '@/utils/request'
// import axios from 'axios'
/**
 *   获取调整审核查询数据
 * @param {object} 查询实体
 */
export function getAdjustAudit(data) {
  return request({
    url: '/consumables/consumablesSafeStock/findAdjustAuditPage',
    method: 'post',
    data: data
  })
}
/**
 *   获取审核按钮数据
 * @param {object} 查询实体
 */
export function passOrReject(data) {
  return request({
    url: '/consumables/consumablesSafeStock/passAdjust',
    method: 'post',
    data: data
  })
}
/**
 *   获取驳回数据
 * @param {object} 查询实体
 */
export function getrejectAdjust(data) {
  return request({
    url: '/consumables/consumablesSafeStock/rejectAdjust',
    method: 'post',
    data: data
  })
}

/**
 *   导出
 * @param {object} 查询实体
 */
export function getExportAdjustAuditExcel(data) {
  return request({
    url: '/consumablesSafeStock/exportAdjustAuditExcel',
    method: 'post',
    data: data
  })
}
