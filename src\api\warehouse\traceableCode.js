import request from "@/utils/request";

//页面加载-分页器改变获取列表数据
export function getScanOrder(scanOrder) {
  return request({
    url: "/instock/purchase/codeScanOrder/queryList",
    method: "post",
    data: scanOrder,
  });
}

//追溯码任务返回明细
export function getDetailScanOrder(detailScanOrder) {
  return request({
    url: "/instock/purchase/codeScanOrder/getDetailPurchaseCodeScanOrder",
    method: "post",
    data: detailScanOrder,
  });
}

//查询已扫描的追溯码列表
export function getRegulatory(data) {
  return request({
    url: "/instock/common/queryOrderDrugRegulatoryCode",
    method: "post",
    data: data,
  });
}

//扫描或手动输入追溯码确认
export function getInsertCode(data) {
  return request({
    url: "/instock/purchase/codeScanOrder/insertDrugRegulatoryCodeRecord",
    method: "post",
    data: data,
  });
}

//根据id删除追溯码
export function deleteScanById(data) {
  return request({
    url: "/instock/purchase/codeScanOrder/deleteOrderDrugRegulatoryCode",
    method: "post",
    data: data,
  });
}

//更新追溯码任务状态
export function updateScan(data) {
  return request({
    url: "/instock/purchase/codeScanOrder/updateScanOrderStatus",
    method: "post",
    data: data,
  });
}

//解锁
export function unLock(data) {
  return request({
    url: "/instock/purchase/codeScanOrder/checkPermissionBystaffNum",
    method: "post",
    data: data,
  });
}

//模糊查询追溯码扫描单编号
export function findScanCode(data) {
  return request({
    url: "/instock/purchase/codeScanOrder/querySupervisionCode",
    method: "post",
    data: data,
  });
}
