<template>
  <div class="xyy-button">
    <button
      :disabled="disabled || loading"
      :loading="loading"
      type="button"
      :class="{'btn':true,'is-loading':loading}"
      @click="clickEvent"
    >

      <span class="sMag">{{ label }}</span>
      <span class="sMag">{{ shortkey }}</span>
      <i v-if="loading" class="btn-loading el-icon-loading" />
    </button>

  </div>
</template>

<script>
export default {
  name: 'XyyButton',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    shortkey: {
      type: String,
      default: ''
    }
  },
  mounted() {
    // const hotkeys = this.shortkey.toLowerCase()
    // this.$hotkeys(hotkeys, this.handlerKeys)
  },
  beforeDestroy() {
    // const hotkeys = this.shortkey.toLowerCase()
    // this.$hotkeys.unbind(hotkeys)
  },
  methods: {
    clickEvent() {
      this.$emit('click')
    },
    handlerKeys(event, handler) {
      event.preventDefault()
      this.clickEvent()
    }
  }
}
</script>
<style lang="scss" scoped>
.xyy-button{
  display: inline-block;
  .btn {
    position: relative;
    height: 32px;
    color: #fff;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    vertical-align: top;
    touch-action: manipulation;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    background-color: #2DB7F5;
    border-color: #2DB7F5;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.42857143;
    border-radius: 4px;
    font-size: 14px;
    min-width: 54px;
    padding: 0 12px;
    margin-right: 10px;
    .btn-loading{
      position: relative;
      top: -18px;
      color: #2DB7F5;
    }
    &.is-loading::before{
      pointer-events: none;
      content: "";
      position: absolute;
      left: -1px;
      top: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: inherit;
      background-color: rgba(0, 0, 0, 0.6);
    }
    &:disabled{
      cursor: not-allowed;
      background-color: #a0cfff;
      border-color: #a0cfff;
    }
    &:last-child{
      margin-right: 0px;
    }
    .sMag{
      display: inline-block;
      height: 15px;
      line-height: 15px;
      font-size: 12px;

    }
  }
}
</style>

