<template>
  <div class="xyy-detail-group">
    <el-col
      v-for="(item,index) in datas"
      :key="1000+index"
      :md="item.col"
      :lg="item.col"
    >
      <div class="header-row">
        <span :style="{width:item.titleWidth}">{{item.title}}</span>
        <span class="header-content-row">{{item.content}}</span>
      </div>
    </el-col>
  </div>
</template>

<script>
export default {
  name: "XyyDetailGroup",
  props: {
    datas: [],
  },
};
</script>

<style scoped>
.header-row {
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>