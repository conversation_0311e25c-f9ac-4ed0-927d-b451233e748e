import request from "@/utils/request";

//【取消订单】出库订单列表
export function stockoutCancelOrder(data) {
    return request({
        url: '/outstock/web/outstock/salesorder/getAllOrderInfo',
        method: 'post',
        data
    })
}

//【取消订单】取消订单列表
export function stockoutCancelOrderList(data) {
    return request({
        url: '/outstock/web/outstock/salesorder/getCancelOrder',
        method: 'post',
        data
    })
}

//【取消订单】取消订单
export function stockoutCancelOrderCancel(data) {
    return request({
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/outstock/web/outstock/salesorder/cancelOrder',
        method: 'post',
        params: data
    })
}

//【取消订单】订单详情
export function stockoutCancelOrderDetail(data) {
    return request({
        url: '/outstock/web/outstock/salesorder/detail',
        method: 'post',
        data
    })
}