import request from "@/utils/request";
import { stringify } from "qs";

/**
 *   称重-扫描拼箱、整件条、运单号
 * @param {object} 查询实体
 */
export function scanWeightBarcode(data) {
  return request({
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    url: "/outstock/web/outstock/weight/scanWeightBarcode",
    method: "post",
    params: data,
  });
}

export function getWeightWms(data) {
  return request({
    url: "stockout/openCom/getWeight/wms",
    method: "get",
    data: data,
  });
}
export function loadWeightUnPrint(data) {
  return request({
    url: "/outstock/web/outstock/weight/loadWeightUnPrint",
    method: "get",
    params: data,
  });
}
export function loadWeightUnPrintItem(data) {
  return request({
    url: "/outstock/web/outstock/weight/loadWeightUnPrintItem",
    method: "post",
    data: data
  });
}
//称重-打印交接单
export function pringDelivery(data) {
  return request({
    url: "stockout/weight/pringDelivery",
    method: "post",
    data: data,
  });
}

//称重-打印交接单
export function pringExpress(data) {
  return request({
    url: "/outstock/web/outstock/weight/pringExpress",
    method: "post",
    data: data,
  });
}

//称重-保存
export function saveWeight(data) {
  return request({
    url: "/outstock/web/outstock/weight/saveWeight",
    method: "post",
    data: data,
  });
}

//称重-历史打印汇总
export function loadWeightHistoryPrint(data) {
  return request({
    url: "/outstock/web/outstock/weight/loadWeightHistoryPrint",
    method: "post",
    data: data,
  });
}

//称重-历史打印明细
export function loadWeightHistoryPrintItem(data) {
  return request({
    url: "/outstock/web/outstock/weight/loadWeightHistoryPrintItem",
    method: "post",
    data: data,
  });
}

export function pringDeliveryHistory(data) {
  return request({
    url: "stockout/weight/pringDeliveryHistory",
    method: "post",
    data: data,
  });
}

export function loadUserInfo(data) {
  return request({
    url: "stockout/weight/loadUserInfo",
    method: "post",
    data: data,
  });
}

export function changeCarrier(data) {
  return request({
    url: "stockout/weight/changeCarrier",
    method: "post",
    data: data,
  });
}

export function changeCarrierScan(data) {
  return request({
    url: "stockout/weight/changeCarrierScan",
    method: "post",
    data: data,
  });
}
export function findWeightItemInfo(data) {
  return request({
    url: "/outstock/web/outstock/weight/findWeightItemInfo",
    method: "post",
    data: data,
  });
}

export function updateWeight(data) {
  return request({
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    url: "/outstock/web/outstock/weight/updateWeight",
    method: "post",
    data: stringify(data),
  });
}
export function exportWeightItemInfo(data) {
  return request({
    url: "/export/submitExportTask",
    method: "post",
    data: data,
  });
}
export function selectWaitWeightCountAll(data) {
  return request({
    url: "/outstock/web/outstock/weight/selectWaitWeightCountAll",
    method: "get",
    data,
  });
}

//查询待称重明细
export function selectWaitWeightItem(data) {
  return request({
    url: "/outstock/web/outstock/weight/selectWaitWeightItem",
    method: "post",
    data: data,
  });
}

//快速称重
export function selectWeightQuickWeight(data) {
  return request({
    url: "/outstock/web/outstock/weight/quickWeight",
    method: "post",
    params: data,
  });
}


