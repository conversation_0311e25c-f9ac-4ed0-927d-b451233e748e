self.onmessage = function (params) {
  let datas = getParams(params.data);
  self.postMessage(datas);
};

function getParams(checkRows) {
  let params = [];
  checkRows.data.forEach((item) => {
    item.autoColumns.forEach((item1) => {
      const row = {
        memberCode: item.memberCode,
        shiftId: item1.shiftId,
        date: item1.fullDate,
        calendarId: checkRows.id,
        shiftType: checkRows.shiftType,
      };
      params.push(row);
    });
  });
  return params;
}
