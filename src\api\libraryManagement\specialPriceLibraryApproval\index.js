import request from '@/utils/request'

/**
 * 特价库列表查询
 * @param {Object} data
 */
export function apiWarehouseMovementTJKPage(params) {
    return request({
        url: 'warehouse/warehouseMovement/tjk/page',
        method: 'post',
        data: params
    })
}

/**
 * 获取业主集合
 * @param {Object} data
 */
export function apiGetOwnerInfo(params) {
    return request({
        url: '/salesreturnCommon/getOwnerInfo',
        method: 'post',
        data: params
    })
}

/**
 * 特价库列表导出
 * @param {Object} data
 */
export function apiWarehouseMovementTJKPageExport(params) {
    return request({
        url: 'warehouse/warehouseMovement/tjk/page/export',
        method: 'post',
        data: params
    })
}

/**
 * 审批详情
 * @param {Object} data
 */
export function apiWarehouseMovementTJKDetail(params) {
    return request({
        url: 'warehouse/warehouseMovement/getWarehouseApprovalByNo',
        method: 'post',
        data: params
    })
}

/**
 * 录入-提取
 * @param {Object} data
 */
export function apiWarehouseMovementTJKExtract(params) {
    return request({
        url: 'warehouse/warehouseMovement/extract',
        method: 'post',
        data: params
    })
}

/**
 * 特价库详情导出
 * @param {Object} data
 */
export function apiWarehouseMovementTJKDetailExport(params) {
    return request({
        url: '/warehouseMovement/tjk/detail/export',
        method: 'post',
        data: params
    })
}

/**
 * 特价库详情导出
 * @param {Object} data
 */
export function apiWarehouseMovementTJKApproval(params) {
    return request({
        url: 'warehouse/warehouseMovement/approval',
        method: 'post',
        data: params
    })
}

