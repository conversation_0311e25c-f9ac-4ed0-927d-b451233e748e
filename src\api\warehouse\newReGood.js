import request from '@/utils/request'

/**
 *  .收货->新建收货单初始化
 * @param {  } 
 */
export function receiveOrderV2Get() {
  return request({
    url: '/instock/receiveOrder/get',
    method: 'post',
    data:{}
  })
}
/**
 * 批号模糊搜索
 * @param {object} 查询实体
 */
 export function getBatchCode(data) {
  return request({
    url: '/instock/common/queryProductBatchCode',
    method: 'get',
    params: data
  })
}


/**
 * 提交
 * @param {object} 查询实体
 */
 export function receiveOrderSubmit(data) {
  return request({
    url: '/instock/receiveOrder/submit',
    method: 'post',
    data: data
  })
}

/**
 * 保存
 * @param {object} 查询实体
 */
 export function receiveOrderSave(data) {
  return request({
    url: '/instock/receiveOrder/save',
    method: 'post',
    data: data
  })
}

/**
 *3.收货->提取采购单
 * @param {object} 查询实体
 */
 export function getListByOrder(data) {
  return request({
    url: '/instock/purchaseOrder/findList',
    method: 'post',
    data: data
  })
}

/**
 * 收货->提取采购单->查询明细
 * @param {object} 查询实体
 */
 export function findListByOrder(data) {
  return request({
    url: '/instock/purchaseOrder/findListByOrder',
    method: 'post',
    data: data
  })
}

/**
 * 收货->初始化按订单提取
 * @param {object} 查询实体
 */
 export function getOptions(data) {
  return request({
    url: '/instock/receiveOrder/initExtractByOrder',
    method: 'post',
    data: data
  })
}
