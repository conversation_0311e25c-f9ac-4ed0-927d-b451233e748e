import request from "@/utils/request";

//收货统计查询
export function getPurchaseStatistics(data) {
    return request({
        url: "/instock/pieceworkStatistics/Consignees",
        method: "post",
        data
    });
}

//验收统计查询
export function getPurchaseStatisticsCheck(data) {
    return request({
        url: "/instock/pieceworkStatistics/verifyAccept",
        method: "post",
        data
    });
}

//上架统计查询
export function getPurchaseStatisticsUp(data) {
    return request({
        url: "/instock/pieceworkStatistics/upShelves",
        method: "post",
        data
    });
}

//入库内部差错
export function getPurchaseStatisticsError(data) {
    return request({
        url: "/instock/pieceworkStatistics/internalError",
        method: "post",
        data
    }
    );
}