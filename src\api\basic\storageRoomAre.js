import request from '@/utils/request';
/************基础资料************* */
/**
 * 基础资料-货位档案-库房库区字典
 */

//查询库房库区列表
export function getStorageRoomAreaList(params) {
    return request({
        url: '/basicdata/storageRoomArea/getStorageRoomAreaList',
        method: 'post',
        data: params
    })
}

//库别字段
export function getAllStorageType() {
    return request({
        url: '/basicdata/dictBases/getAllStorageType',
        method: 'post',
    })
}

//联动库别查询所有库房
export function getAllStorageRoomByType(param) {
    return request({
        url: '/basicdata/dictBases/getAllStorageRoomByType',
        method: 'post',
        data: param
    })
}

//添加库房库区
export function addStorageRoomArea(param) {
    return request({
        url: '/basicdata/storageRoomArea/save',
        method: 'post',
        data: param
    })
}

//删除库房库区
export function deleteStorageRoomArea(param) {
    return request({
        url: '/basicdata/storageRoomArea/delete',
        method: 'post',
        data: param
    })
}

//获取库房条件
export function getStorageRoomAreaCondition(param) {
    return request({
        url: '/basicdata/storageRoomArea/getStorageRoomAreaCondition',
        method: 'get',
        params: param
    })
}

//查询库区类型
export function getStorageRoomAreaType() {
    return request({
        url: '/basicdata/dictBases/getAllStorageAreaType',
        method: 'post',
    })
}