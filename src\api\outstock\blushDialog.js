import request from '@/utils/request'

// 冲红原因列表
export function blushReasonAllLists(data) {
  return request({
    url: '/outstock/web/common/listRushRedReason',
    method: 'post',
    data
  })
}
// 正常出库订单——出库单冲红明细列表
export function getBlushDetailLists(data) {
  return request({
    url: '/outstock/web/outstock/salesorder/rushred/listRushRedDetail',
    method: 'post',
    data
  })
}
// 正常出库订单——保存冲红
export function confirmToRushRed(data) {
  return request({
    url: '/outstock/web/outstock/salesorder/rushred/saveRushRed',
    method: 'post',
    data
  })
}