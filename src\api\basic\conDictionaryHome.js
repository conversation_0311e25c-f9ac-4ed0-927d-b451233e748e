import request from '@/utils/request';

/************基础资料 start************* */
/**
 * 容器-容器字典/容器状态 共用分页
 */
export function containerPageContainer(param) {
  return request({
    url: '/basicdata/container/pageContainer',
    method: 'post',
    data: param,
  });
}

/************基础资料 start************* */
/**
 * 数据字典-容器类型
 */
export function getByDictTypeList() {
  return request({
    url: '/basicdata/dictBases/getByDictTypeList',
    method: 'post',
    data: { items: ['RQLX'] },
  });
}

/************基础资料 start************* */
/**
 * 容器-容器字典新增
 */
export function addContainer(data) {
  return request({
    url: '/basicdata/container/addContainer',
    method: 'post',
    data: data,
  });
}

/************基础资料 start************* */
/**
 * 容器-容器字典修改
 */
export function updateContainer(data) {
    return request({
      url: '/basicdata/container/updateContainer',
      method: 'post',
      data: data,
    });
  }

/************基础资料 start************* */
/**
 * 容器-容器字典打印
 * */
export function printContainer(data) {
    return request({
      url: '/basicdata/container/printBarCode',
      method: 'post',
      data: data,
    });
}
  