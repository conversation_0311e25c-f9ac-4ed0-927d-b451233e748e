import request from '@/utils/request'


/************基础资料 start************* */
/**
 * 获取字典
 */
export function getByDictType(params) {
    return request({
        url: '/basicdata/dictParam/getByCode',
        method: 'post',
        data:params
    })
}

/************基础资料 start************* */
/**
 * 分页查询字段列表
 */
export function getDictParamList(param) {
    return request({
        url: '/basicdata/dictParam/getDictParamList',
        method: 'post',
        data:param
    })
}

/************基础资料 start************* */
/**
 * 数据字典-容器类型
 */
export function getByDictTypeList(param) {
    return request({
        url: '/basicdata/dictBases/getByDictTypeList',
        method: 'post',
        data:param
    })
}

/************基础资料 start************* */
/**
 * 数据字典-容器类型-更新
 */
export function update(param) {
    return request({
        url: '/basicdata/dictParam/update',
        method: 'post',
        data:param
    })
}



