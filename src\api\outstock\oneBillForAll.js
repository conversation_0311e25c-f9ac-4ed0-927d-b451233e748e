import request from '@/utils/request'
/**
 *   配送路线
 * @param {object} 查询实体
 */
 export function getDeliveryRoute(params) {
    return request({
      url: 'master/customerBase/vagueDeliveryRoute',
      method: 'post',
      data: params
    })
  }
/**
 *   客户订单管理-分页查询
 * @param {object} 查询实体
 */
 export function customerOrderControllerFindPage(params) {
    return request({
      url: '/outstock/web/outstock/packtask/list',
      method: 'post',
      data: params
    })
  }

/**
 *   客户订单管理-生成提总单
 * @param {object} 查询实体
 */
 export function customerOrderControllerPackTotal(params) {
    return request({
      url: '/outstock/web/outstock/packtask/pack',
      method: 'post',
      data: params
    })
  }

/**
 *   客户订单管理-生成杂单
 * @param {object} 查询实体
 */
 export function customerOrderControllerPackMiscellaneous(params) {
  return request({
    url: '/outstock/web/outstock/packtask/pack',
    method: 'post',
    data: params
  })
}
/**
 * 生成电子标签
 */
export function createOnlineTag(params) {
  return request({
    url: '/outstock/web/outstock/packtask/dpsPack',
    method: 'post',
    data: params
  })
}
/**
 *   客户订单管理-前置仓-生成拣货任务
 * @param {object} 查询实体
 * */
 export function customerOrderControllerPrePick(params) {
    return request({
      url: '/outstock/web/outstock/packtask/frontPackTotal',
      method: 'post',
      data: params
    })
 }
