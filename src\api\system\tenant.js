import request from '@/utils/request'

// 使用租户名，获得租户编号
export function getTenantIdByName(name) {
  return request({
    url: '/basicdata/system/tenant/get-id-by-name',
    method: 'post',
    data: {
      name:name
    }
  })
}

// 创建租户
export function createTenant(data) {
  return request({
    url: '/basicdata/system/tenant/create',
    method: 'post',
    data: data
  })
}

// 更新租户
export function updateTenant(data) {
  return request({
    url: '/basicdata/system/tenant/update',
    method: 'post',
    data: data
  })
}

// 删除租户
export function deleteTenant(id) {
  return request({
    url: '/basicdata/system/tenant/delete',
    method: 'post',
    data: {id:id}
  })
}

// 获得租户
export function getTenant(id) {
  return request({
    url: '/basicdata/system/tenant/get',
    method: 'post',
    data: {id:id}
  })
}

// 获得租户分页
export function getTenantPage(query) {
  return request({
    url: '/basicdata/system/tenant/page',
    method: 'post',
    data: query
  })
}

// 导出租户 Excel
export function exportTenantExcel(query) {
  return request({
    url: '/basicdata/system/tenant/export-excel',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}


/**
 * 商品资料-枚举接口
 */
export function queryGoodsinfoDictByType(param) {
  return request({
      url: '/basicdata/dictBases/getByDictTypeList',
      method: 'post',
      data: param
  })
}