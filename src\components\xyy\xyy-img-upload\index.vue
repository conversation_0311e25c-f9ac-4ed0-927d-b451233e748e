<template>
  <el-dialog
    :title="title"
    :visible="dialogVisible"
    :close-on-click-modal="false"
    :width="width"
    :before-close="handleClose"
    append-to-body
    @on-close="handleClose"
  >
    <div v-if="isRow">
      <div style="margin-bottom:10px">
        <span>商品编码：{{ row.productCode }}</span><span style="margin-left:20px;">商品名称：{{ row.productName }}</span>
      </div>
      <div style="margin-bottom:10px;">上传对应商品异常图片</div>
    </div>
    <el-upload
      ref="elUpload"
      class="xyy-upload clearfix"
      :action="action"
      :accept="accept"
      :http-request="uploadFunction"
      :list-type="listType"
      :on-change="handleChange"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :auto-upload="singleUpload"
      :before-upload="beforeUpload"
      :limit="limit"
      :multiple="true"
    >
      <i slot="default" class="el-icon-plus" id="uploadEl"></i>
    </el-upload>
    <slot />

    <span slot="footer" class="dialog-footer">
      <el-button @click="xuanzhuan" v-if="isgpy">旋转</el-button>
      <el-button @click="starGPY" v-if="isgpy">启动高拍仪</el-button>
      <el-button @click="stropGPY" v-if="isgpy">停止高拍仪</el-button>
      <el-button @click="camPhoto" v-if="isgpy||isCamera">拍照</el-button>
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleOk" :loading="loading">确 定</el-button>
    </span>
    <el-dialog :visible.sync="PreviewDialogVisible" style="z-index: 1;">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog' // 拖拽指令

export default {
  name: 'XyyImgUpload',
  directives: { elDragDialog },
  props: {
    isRow:{
      type:Boolean,
      default:false
    },
    isgpy:{
      type: Boolean,
      default: false},
    isCamera:{
      type:Boolean,
      default:false
    }, 
    loading: {
      type: Boolean,
      default: false
    },
    title: { // 标题
      type: String,
      default: ''
    },
    width: { // 弹窗宽度
      type: String,
      default: '648px'
    },
    action: { // 上传路径
      type: String,
      default: ''
    },
    multiple: { // 是否多选
      type: Boolean,
      default: false
    },
    limit: { // 文件数量限制，默认100
      type: Number,
      default: 100
    },
    accept: { // 接收文件类型
      type: String,
      default: ''
    },
    autoUpload: { // 自动提交
      type: Boolean,
      default: false
    },
    singleUpload:{ // 单个提交-是否在选取文件后立即上传
      type: Boolean,
      default: false
    },
    listType:{
      type: String,
      default:''
    },
    editFileList:{
      type:Array,
      default:(()=>{
        return []
      })
    },
    uploadFunction:{
      type:Function,
      default:undefined
    },
    beforeUpload:{
      type:Function,
      default:undefined
    },
    row:{
      Object,
      default:{}
    }
  },
  watch:{
    editFileList(newvalue,oldvalue){
      console.log(newvalue,'newvalue');
      this.fileList = newvalue;
    },
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      dialogImageUrl: '',
      PreviewDialogVisible: false
    }
  },
  methods: {  
    close() {
      this.clearFiles();
      this.dialogVisible = false
      this.$emit('on-close')
    },
    open() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('on-close')
    },
    // 上传文件
    submitUpload() {
      this.$refs.elUpload.submit()
    },
    // 清空文件
    clearFiles() {
      this.$refs.elUpload.clearFiles()
    },
    handleRemove(file, fileList) {
      console.log(fileList,'-----------');
      this.$emit('on-change', file, fileList)
    },
    handlePictureCardPreview(file, fileList){
        this.dialogImageUrl = file.url;
        this.PreviewDialogVisible = true;
    },
    // 文件改变时回调
    handleChange(file, fileList) {
      this.$emit('on-change', file, fileList)
    },
    handleExceed(files, fileList) {
      if (this.limit === 1) {
        this.$set(fileList[0], 'raw', files[0])
        this.$set(fileList[0], 'name', files[0].name)
        this.clearFiles() // 清除文件
        this.$refs['elUpload'].handleStart(files[0]) // 选择文件后的赋值方法
      }
    },
    // 确定回调
    handleOk() {
      this.autoUpload && this.submitUpload()
      this.$emit('on-confirm', this.fileList)
    },
    // 上传接口成功回调
    handleSuccess(response, file, fileList) {
      this.$emit('on-success', response, file, fileList)
    },
    stropGPY(){
      this.$emit('stopgpy')
    },
    starGPY(){
      this.$emit('stargpy');
    },

    setfile(file){
      this.fileList.push(file);
    },
    camPhoto(){
      if(this.isCamera){
        this.$emit("cameraHandler")
      }else{
        this.$emit('camPhoto');
      }
    },
    xuanzhuan(){
      this.$emit('xuanzhuan');
    },
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__wrapper{
  z-index: 99999 !important;
}
</style>
