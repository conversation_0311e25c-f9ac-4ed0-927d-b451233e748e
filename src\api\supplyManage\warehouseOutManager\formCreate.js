import request from '@/utils/request'

/**
 *  申领单创建
 * @param {object} 查询实体
 */
export function saveStockoutBill(params) {
  return request({
    url: '/consumables/stockout/saveStockoutBill',
    method: 'post',
    data: params
  })
}

/**
 *  申领单出库列表
 * @param {object} 查询实体
 */
export function getStockOutBillList() {
  return request({
    url: '/consumables/stockout/stockoutBillList',
    method: 'post'
  })
}

/**
 *  申领单出库详情列表
 * @param {object} 查询实体
 */
export function getStockOutBillDetailList(params) {
  return request({
    url: '/consumables/stockout/stockoutBillDetailList',
    method: 'post',
    data: params
  })
}

/**
 *  申领单出库确认or驳回
 * @param {object} 查询实体
 */
export function stockoutApprovalt(params) {
  return request({
    url: '/consumables/stockout/stockoutApproval',
    method: 'post',
    data: params
  })
}

/**
 *  申领单明细列表-保存出库备注
 * @param {object} 查询实体
 */
export function saveStockoutRemark(params) {
  return request({
    url: '/consumables/stockout/saveStockoutRemark',
    method: 'post',
    data: params
  })
}

/**
 *  申领单导出
 * @param {object} 查询实体
 */
export function getExportStockoutExcel(data) {
  return request({
    url: '/stockout/exportStockoutExcel',
    method: 'post',
    data
  })
}

