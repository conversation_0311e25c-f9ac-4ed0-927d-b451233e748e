import request from '@/utils/request'

// 查询岗位列表
export function listPost(query) {
  return request({
    url: '/basicdata/system/post/page',
    method: 'post',
    data: query
  })
}

// 获取岗位精简信息列表
export function listSimplePosts() {
  return request({
    url: '/basicdata/system/post/list-all-simple',
    method: 'post'
  })
}

// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/basicdata/system/post/get',
    method: 'post',
    data:{id:postId}
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/basicdata/system/post/create',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: '/basicdata/system/post/update',
    method: 'post',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return request({
    url: '/basicdata/system/post/delete',
    method: 'post',
    data: {id: postId}
  })
}

// 导出岗位
export function exportPost(query) {
  return request({
    url: '/basicdata/system/post/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
