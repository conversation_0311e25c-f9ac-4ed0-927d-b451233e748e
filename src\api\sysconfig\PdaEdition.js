import request from "@/utils/request";

//查询新增信息
export function getVersionList() {
  return request({
    url: "basicdata/version/list",
    method: "post",
  });
}

//查询新增信息
export function getVersionOrgList() {
  return request({
    url: "/gateway/basicdata/versionOrgList",
    method: "post",
  });
}

//新增
export function addPublish(data) {
  return request({
    url: "basicdata/version/publish",
    method: "post",
    data: data,
  });
}

//新增
export function deletePublic(data) {
  return request({
    url: "basicdata/version/disable",
    method: "post",
    data: data,
  });
}