import request from '@/utils/request'

/** 耗材管理 **/
/**
 *  查询物品列表
 * @param {object} 查询实体
 */
export function getPageList(params) {
  return request({
    url: '/consumables/basicdata/queryPagerList',
    method: 'post',
    data: params
  })
}

/**
 * 新增/更新 物品信息
 * @param {*} data id  （传id编辑，不传id新增）
 */
export function updateGoods(data) {
  return request({
    url: '/consumables/basicdata/insertOrUpdate',
    method: 'post',
    data: data
  })
}

/**
 * 通过物品编码查询Vo
 * @param {String} consumablesCode 物品编码（必传项）
 */
export function getConsumablesInfoVo(consumablesCode) {
  return request({
    url: 'consumables/basicdata/queryConsumablesInfoVo',
    method: 'post',
    data: { consumablesCode }
  })
}

/**
 * 耗材资料导出
 * @param {Object}  请求参数
 */
export function consumablesDataExport(data) {
  return request({
    url: 'consumables/basicdata/exportDataExcel',
    method: 'post',
    data: data
  })
}

/**
 * 耗材资料导入
 * @param {Object}  请求参数
 */
export function consumablesDataImport(data) {
  return request({
    url: 'consumables/basicdata/exportDataExcel',
    method: 'post',
    data: data
  })
}

/**
 * 物品编码修改
 * @param {Object}  请求参数
 */
export function updateConsumablesCode(data) {
  return request({
    url: '/consumables/basicdata/updateCode',
    method: 'post',
    data: data
  })
}
//文件流下载  
/**
 * 商品检验报告-检验报告下载
 * @param {Object} data
 */
 export function drugTestReportDownload() {
  return request({
    url: '/consumables/basicdata/exportExcel',
    method: 'post',
    params: {},
    responseType: 'blob'
  })
}
//文件流下载 -导入错误下载 
/**
 * 商品检验报告-检验报告下载
 * @param {Object} data
 */
export function downLoadErrorData(params) {
  return request({
    url: '/consumables/basicdata/downLoadErrorData',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
