import request from '@/utils/request'

// 销售退回入库单列表分页查询
export function saleBackPushStockSearch(data) {
  return request({
    url: '/instock/salesreturn/inbound/queryInboundListPage',
    method: 'post',
    data
  })
}
// 销售退回入库单详情录入上架结果以及商品明细查询
export function pushStockDetailResultSearch(data) {
  return request({
    url: '/instock/salesreturn/inbound/findInboundAndDetailsByCode',
    method: 'post',
    data
  })
}
// 销售退回入库单提交
export function pushStockConfirmSubmit(data) {
  return request({
    url: '/instock/salesreturn/inbound/submit',
    method: 'post',
    data
  })
}
// 销退打印上架单
export function pushStockPrinter(data) {
  return request({
    url: '/instock/salesreturn/inbound/print',
    method: 'post',
    data
  })
}
