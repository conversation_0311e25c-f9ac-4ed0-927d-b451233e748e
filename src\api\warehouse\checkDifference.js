import request from '@/utils/request'

//盘点差异单分页查询
export function getCheckDifferenceList(data) {
    return request({
        url: '/warehouse/warehouseCheckDifference/selectCheckDifferenceList',
        method: 'post',
        data
    })
}

//根据id删除盘点差异单
export function deleteCheckDifferenceById(data) {
    return request({
        url: '/warehouse/warehouseCheckDifference/deleteByIds',
        method: 'post',
        data: data
    })
}

//重建盘点单
export function rebuildCheckDifference(data) {
    return request({
        url: '/warehouse/warehouseCheckDifference/reCreateCheckOrder',
        method: 'post',
        data: data
    })
}