<template>
  <vxe-table
    ref="xTable"
    :loading="loading"
    :height="height"
    :data="tableData"
    :row-class-name="rowClassName"
    :seq-config="seqConfig"
    :highlight-current-row="highlightCurrentRow"
    :highlight-hover-row="highlightHoverRow"
    @current-change="currentChange"
  >
    <slot></slot>
  </vxe-table>
</template>

<script>
import Sortable from "sortablejs";
export default {
  props: {
    needSeq: { type: Boolean, default: false },
    needCheckBox: { type: Boolean, default: false },
    columns: { type: Array, default: () => [] },
    data: { type: Array, default: () => [] },
    loading: { type: Boolean, default: false },
    height: { type: String, default: "auto" },
    seqConfig: { type: Object },
    rowClassName: [String, Function],
    highlightCurrentRow: {type: Boolean, default: false},
    highlightHoverRow: {type: <PERSON>olean, default: false},
  },
  data() {
    return {
      tableColumns: [],
      tableData: [],
    };
  },
  watch: {
    columns(val) {
      this.tableColumns = val;
    },
    data(val) {
      this.tableData = val;
    },
  },
  mounted() {
    this.tableColumns = this.columns;
    this.getSortTable();
  },
  methods: {
    getSortTable() {
      this.$nextTick(() => {
        const xTable = this.$refs.xTable;
        if (!xTable) {
          return;
        }
        const el = xTable.$el.querySelector(".vxe-header--row");
        this.sortTable = new Sortable.create(el, {
          handle: ".vxe-header--column",
          filter: this.disMoveFunction,
          animation: 150,
          onEnd: (evt) => {
            const oldIndex = evt.oldIndex;
            const newIndex = evt.newIndex;

            const passcount = this.passIndex();
            if (newIndex > passcount && oldIndex > passcount) {
              const oldData = this.tableColumns[oldIndex - passcount];
              this.tableColumns.splice(oldIndex - passcount, 1);
              this.tableColumns.splice(newIndex - passcount, 0, oldData);
              console.log(
                oldIndex - passcount,
                newIndex - passcount,
                this.tableColumns
              );
            }

            this.refreshTable();
          },
        });
      });
    },

    //禁止拖拽
    disMoveFunction(v,evt, target){
        if(evt._prevClass.indexOf('col--seq') !== -1){
            return ".col--seq";
        }
        if(evt._prevClass.indexOf('col--checkbox') !== -1){
            return ".col--checkbox";
        }

        
    },
    //前多少列不可切换
    passIndex() {
      let index = 0;
      if (this.needSeq) {
        index += 1;
      }
      if (this.needCheckBox) {
        index += 1;
      }
      return index;
    },
    // 刷新表格
    refreshTable() {
      const columns = _.cloneDeep(this.tableColumns);
      const table = _.cloneDeep(this.tableData);
      this.$emit("update-table", columns, table);
    },
    currentChange(val1, val2, val3, val4) {
      this.$emit("current-change", val1, val2, val3, val4);
    },
  },
};
</script>

<style scoped>
</style>