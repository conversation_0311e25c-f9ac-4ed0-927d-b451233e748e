import request from '@/utils/request'
/**
 * 库存管理-库存查询
 * @param {object}
 */
export function apiGetSockList(param) {
    return request({
      url: '/equityCard/storageGoodsAllocation/pageQuery',
      method: 'post',
      data:param
    })
}
/**
 * 导出 - 库存列表
 * @param {object}
 */
export function exportStockList(data) {
    return request({
      url: '/equityCard/storageGoodsAllocation/pageExport',
      method: 'post',
      data
    })
}
/**
 * 库存管理-卡序查询
 * @param {object}
 */
export function apiGetCarNoList(param) {
  return request({
    url: '/equityCard/storageGoodsAllocationDetail/pageQuery',
    method: 'post',
    data: param
  })
}
/**
 * 导出 - 卡序列表
 * @param {object}
 */
export function exportCarNoList(data) {
  return request({
    url: '/equityCard/storageGoodsAllocationDetail/pageExport',
    method: 'post',
    data
  })
}
/**
 * 库存管理-库存账页查询
 * @param {object}
 */
export function apiGetStockBillList(param) {
  return request({
    url: '/equityCard/storageLedgerPaper/pageQuery',
    method: 'post',
    data: param
  })
}
/**
 * 导出 - 库存账页
 * @param {object}
 */
export function exportStockBillList(param) {
  return request({
    url: '/equityCard/storageLedgerPaper/pageExport',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-卡序账页查询
 * @param {object}
 */
export function apiGetCarNoBillList(param) {
  return request({
    url: '/equityCard/storageLedgerPaperDetail/pageQuery',
    method: 'post',
    data: param
  })
}
/**
 * 导出 - 库存账页
 * @param {object}
 */
export function exportCarNoBillList(param) {
  return request({
    url: '/equityCard/storageLedgerPaperDetail/pageExport',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-库存调整提交审核
 * @param {object}
 */
export function apiresubmit(param) {
  return request({
    url: '/equityCard/storage-goods-adjust/resubmit',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-库存调整取消调整
 * @param {object}
 */
export function apicancel(param) {
  return request({
    url: '/equityCard/storage-goods-adjust/cancel',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-库存调整-调整单列表查询
 * @param {object}
 */
export function apigetList(param) {
  return request({
    url: '/equityCard/storage-goods-adjust/order/list',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-库存调整明细-调整单明细列表查询
 * @param {object}
 */
export function apigetDetailList(param) {
  return request({
    url: '/equityCard/storage-goods-adjust/order/detail/list',
    method: 'post',
    data: param
  })
}
/**
 * 库存管理-库存调整明细-调整单明细列表保存
 * @param {object}
 */
export function apiDetailSubmit(param) {
  return request({
    url: '/equityCard/storage-goods-adjust/order/detail/save',
    method: 'post',
    data: param
  })
}
/**
 * 销退管理-销退入库-退货信息录入-销售单号查询
 * @param {object}
 */
export function apiOrderCode(param) {
  return request({
    url: '/equityCard/sales-return/salesOrderPageList',
    method: 'post',
    data: param
  })
}
/**
 * 销退管理-销退入库-退货信息录入-新增商品查询
 * @param {object}
 */
export function apiProductLiteInfoList(param) {
  return request({
    url: '/equityCard/sales-return/productLiteInfoList',
    method: 'post',
    data: param
  })
}
/**
 * 销退管理-销退入库-查询
 * @param {object}
 */
export function apiStorageOrderPageList(param) {
  return request({
    url: '/equityCard/sales-return/storageOrderPageList',
    method: 'post',
    data: param
  })
}
/**
 * 销退管理-销退入库-退货信息录入-销售单号查询表格数据
 * @param {object}
 */
export function apiProductInfoList(param) {
  return request({
    url: '/equityCard/sales-return/productInfoList' + param,
    method: 'get',
  })
}
/**
 * 销退管理-销退入库-退货单提交
 * @param {object}
 */
export function apiReturnSubmit(param) {
  return request({
    url: '/equityCard/sales-return/submit' + param,
    method: 'post',
  })
}
/**
 * 销退管理-销退入库-退货单保存
 * @param {object}
 */
export function apiReturnSave(param) {
  return request({
    url: '/equityCard/sales-return/save',
    method: 'post',
    data:param
  })
}
/**
 * 销退管理-销退入库-退货单修改
 * @param {object}
 */
export function apiReturnUpdate(param) {
  return request({
    url: '/equityCard/sales-return/update',
    method: 'post',
    data:param
  })
}
/**
 * 销退管理-销退入库-销退单详情
 * @param {object}
 */
export function apiDetailSeach(param) {
  return request({
    url: '/equityCard/sales-return/getStorageReturnOrder' + param,
    method: 'get',
  })
}
/**
 * 销退管理-销退入库-销退单导出
 * @param {object}
 */
export function apiDetailExport(param) {
  return request({
    url: '/equityCard/sales-return/detailExportList',
    method: 'post',
    data: param,
  })
}
/**
 * 销退管理-销退入库-销退录入-推荐货位
 * @param {object}
 */
export function recommendGoodsAllocation(param) {
  return request({
    url: '/equityCard/sales-return/recommendGoodsAllocation',
    method: 'post',
    data: param,
  })
}
