import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/basicdata/system/dept/list',
    method: 'post',
    data: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/basicdata/system/dept/list/exclude/' + deptId,
    method: 'post',
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/basicdata/system/dept/get',
    method: 'post',
    data: {id:deptId}
  })
}

// 获取部门精简信息列表
export function listSimpleDepts() {
  return request({
    url: '/basicdata/system/dept/list-all-simple',
    method: 'post'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/basicdata/system/dept/create',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/basicdata/system/dept/update',
    method: 'post',
    data: data
  })
}

// 删除部门
export function delDept(id) {
  return request({
    url: '/basicdata/system/dept/delete',
    method: 'post',
    data:{id:id}
  })
}
