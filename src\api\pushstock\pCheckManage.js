import request from "@/utils/request";

//验收管理-分页查询列表
export function getPageList(data) {
    return request({
        url: "/instock/checkOrder/queryPurchaseCheckOrder",
        method: "post",
        data: data
    })
}

//验收管理-查询外部用户（采购员）
export function getBuyerList(data) {
    return request({
        url: "/basicdata/system/outUser/byName",
        method: "post",
        data: data
    })
}

//验收管理-查询收货员
export function getReceiverList(data) {
    return request({
        url: "/basicdata/system/user/pageByPost",
        method: "post",
        data: data
    })
}

/**
 *  获取商品选择列表
 * @param {object} 查询实体
 */
export function getProductList(data) {
    return request({
    url: '/instock/common/selectListProduct',
    method: 'post',
    data: data
    })
}

/**
 * 获取供应商信息
 * @param {data} 请求体
 */
export function getListSupplier(data) {
    return request({
    url: '/instock/common/selectListSupplier',
    method: 'post',
    data
    })
}
// 上架单据修改
export function getGroudReceiptUpdate(data){
    return request({
        url:'/instock/purchase/receiveErrorModify/findList',
        method:'post',
        data
    })
}
// 上架明细单
export function getGroudReceiptDetail(data){
    return request({
        url:'/instock/purchase/storageOrder/findModifyDetailListByCode',
        method:'post',
        data
    })
}
// 上架修改单列表
export function getGroudUpdateList(data){
    return request({
        url:'/instock/purchase/purchaseModifyOrder/queryPurchaseModifyOrder',
        method:'post',
        data
    })
}
// 字典查询
export function getWordsLists(data){
    return request({
        url:'/basicdata/dictBases/getByDictType',
        method:'get',
        params:data
    })
}
// 上架单据确认修改提交的接口
export function groudReceiptUpdate(data){
    return request({
        url:'/instock/purchase/receiveErrorModify/insertModificationRecord',
        method:'post',
        data
    })
}
// 上架修改单日期校验的接口
export function dateCheck(data){
    return request({
        url:'/instock/common/productValidityConfigCheck',
        method:'post',
        data
    })
}
// 上架修改单驳回
export function groudReceiptReject(data){
    return request({
        url:'/instock/purchase/purchaseModifyOrder/updateToReject',
        method:'post',
        data
    })
}
// 上架修改单审核通过
export function groudReceiptPass(data){
    return request({
        url:'/instock/purchase/purchaseModifyOrder/saveToThrough',
        method:'post',
        data
    })
}