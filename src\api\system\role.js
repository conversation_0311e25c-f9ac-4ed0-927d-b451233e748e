import request from '@/utils/request'

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/basicdata/system/role/page',
    method: 'post',
    data: query
  })
}

// 查询角色（精简)列表
export function listSimpleRoles() {
  return request({
    url: '/basicdata/system/role/list-all-simple',
    method: 'post'
  })
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/basicdata/system/role/get',
    method: 'post',
    data:{id:roleId}
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/basicdata/system/role/create',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/basicdata/system/role/update',
    method: 'post',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/basicdata/system/role/update-status',
    method: 'post',
    data: data
  })
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/basicdata/system/role/delete',
    method: 'post',
    data:{id:roleId}
  })
}

// 导出角色
export function exportRole(query) {
  return request({
    url: '/basicdata/system/role/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

/**
 * 商品资料-枚举接口
 */
export function queryGoodsinfoDictByType(param) {
  return request({
      url: '/basicdata/dictBases/getByDictTypeList',
      method: 'post',
      data: param
  })
}