import request from '@/utils/request'

//业务查询-购进退出查询-购进退出单明细
export function queryPurchaseDetails(params) {
    return request({
      url: "/outstock/web/purchase/professionalWork/queryPurchaseDetails",
      method: "post",
      data: params,
    });
}
//业务查询-购进退出查询-购进退出任务明细查询
export function queryPurchaseTaskDetail(params) {
  return request({
    url: "/outstock/web/purchase/professionalWork/queryPurchaseTaskDetail",
    method: "post",
    data: params,
  });
}
//业务查询-采购入库查询-采购订单明细查询
export function queryFindPageByPurchase(params) {
  return request({
    url: "/instock/purchase/businessQuery/findPageByPurchase",
    method: "post",
    data: params,
  });
}
//业务查询-采购入库查询-入库拒收明细查询
export function queryFindPageByReject(params) {
  return request({
    url: "/instock/purchase/businessQuery/findPageByReject",
    method: "post",
    data: params,
  });
}
//业务查询-采购入库查询-入库跟踪明细查询
export function queryFindTrackDetailList(params) {
  return request({
    url: "/instock/purchase/businessQuery/findTrackDetailList",
    method: "post",
    data: params,
  });
}
//业务查询-销退业务查询-销退收货明细
export function queryReceiveDetailReport(params) {
  return request({
    url:"/instock/salesreturn/businessQuery/queryReceiveDetailReport",
    method:"post",
    data:params,
  });
}

//业务查询-销退业务查询-销退验收明细
export function queryCheckDetailReport(params) {
  return request({
    url:"/instock/salesreturn/businessQuery/queryCheckDetailReport",
    method:"post",
    data:params,
  });
}

//业务查询-销退业务查询-销退上架明细
export function queryInboundDetailReport(params) {
  return request({
    url:"/instock/salesreturn/businessQuery/queryInboundDetailReport",
    method:"post",
    data:params,
  });
}

//业务查询-销售出库查询-订单追踪明细查询
export function externalTrackDetail(params){
  return request({
    url:"/outstock/web/outstock/salesorder/externalTrackDetail",
    method:"post",
    data:params,
  })
}

//业务查询-商品账页查询
export function storageLedger(params){
  return request({
    url:"/warehouse/query/storageLedger",
    method:'post',
    data:params,
  })
}

//业务查询-库存查询
export function storage(params){
  return request({
    url:"/warehouse/query/storage",
    method:'post',
    data:params,
  })
}

//采购入库查询-入库收货明细查询
export function queryReceiveDetail(params){
  return request({
    url:"/instock/purchase/businessQuery/findPageByReceive",
    method:"post",
    data:params,
  })
}

//采购入库查询-入库验收明细查询
export function queryCheckDetail(params){
  return request({
    url:"/instock/purchase/businessQuery/findPageByCheck",
    method:"post",
    data:params,
  })
}

//采购入库查询-入库上架明细查询
export function queryInboundDetail(params){
  return request({
    url:"/instock/purchase/businessQuery/findPageByStorage",
    method:"post",
    data:params,
  })
}

//采购入库查询-验收驳回单管理
export function queryTurnDown(params){
  return request({
    url:"/instock/purchaseOrder/queryTurnDown",
    method:"post",
    data:params,
  })
}

//采购入库查询-验收驳回单管理
export function queryNonValidated(params){
  return request({
    url:"instock/checkImg/queryCheckImgPage",
    method:"post",
    data:params,
  })
}

//入库验收异常图片预览
export function querypreviewImg(params){
  return request({
    url:"instock/checkImg/previewImg",
    method:"post",
    data:params,
  })
}
