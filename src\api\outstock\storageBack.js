import request from "@/utils/request";
import QS from 'qs'

//【复核回库】回库查询列表
export function getStorageBackList(data) {
    return request({
        url: "/outstock/web/outstock/back/getBackTaskList",
        method: "post",
        data
    })
}

//【复核回库】查询明细列表
export function getStorageBackDetailList(data) {
    return request({
        url: "/outstock/web/outstock/back/getBackTaskDetailList",
        method: "get",
        params: data
    })
}

//【复核回库】索取回库任务
export function getStorageBackTask(data) {
    return request({
        url: "/outstock/web/outstock/back/actualClaimTask",
        method: "post",
        data
    })
}

//【复核回库】回库确认
export function confirmStorageBack(data) {
    return request({
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        url: "/outstock/web/outstock/back/confirmTask",
        method: "post",
        data: QS.stringify(data)
    })
}