import request from "@/utils/request";
import { formData } from '@/utils/index'
//入库作>复查->管理->查询
export function getRecheckManage(data) {
    return request({
        url: "/instock/recheckOrder/page",
        method: "post",
        data: data
    })
}

//入库作>复查->管理->查询复查单执行页面列表
export function getRecheckManageList(data) {
    return request({
        url: "/instock/recheckOrder/getRecheckOrderDetailByCode",
        method: "post",
        data: data
    })
}

//入库作>复查->管理->复查单打印
export function getRecheckManagePrint(data) {
    return request({
        url: "/instock/purchase/storageOrder/upShelfPrint",
        method: "post",
        data: data
    })
}

//入库作>复查->管理->复查单提交操作
export function getRecheckManageSubmit(data) {
    return request({
        url: "/instock/recheckOrder/submit",
        method: "post",
        data: data
    })
}

/**
 *  获取复查执行页面信息
 * @param {object} 查询实体
 */
export function getRecheckOrder(params) {
    return request({
        url: '/instock/recheckOrder/getRecheckOrderDetailByCode',
        method: 'post',
        data: params
    })
}

/**
 *  获取复查查询条件
 * @param {object} 查询实体
 */
export function getRecheckOrderByCode(data) {
    return request({
        url: '/instock/recheckOrder/getRecheckOrderDetailByCode',
        method: 'post',
        data: data
    })
}

/**
 *  绑定，取消绑定
 * @param {object} 查询实体
 */
export function bindCheckOrder(data) {
    return request({
        url: 'purchase/recheckOrder/bindOrNotPurchaseRecheckOrderCancel',
        method: 'post',
        data: formData(data)
    })
}

/**
 *  不合格事项模糊搜索
 * @param {object} 查询实体
 */
export function getReason(params) {
    return request({
        url: '/instock/common/getDictByDictType',
        method: 'post',
        data: params
    })
}

/**
*  上架区域信息
* @param {object} 查询实体
*/
export function getShelfArea(data) {
    return request({
        url: '/instock/common/getStorageTypeAreas',
        method: 'post',
        data: data
    })
}

/**
*  获取商品校验
* @param {object} 查询实体
*/
export function productValidityBatchCheck(data) {
    return request({
        url: '/instock/common/productValidityBatchCheck',
        method: 'post',
        data: data
    })
}

/**
*  获取提交请求
* @param {object} 查询实体
*/
export function submitCheck(data) {
    return request({
        url: '/instock/recheckOrder/submit',
        method: 'post',
        data: data
    })
}

/**
*  获取取消请求
* @param {object} 查询实体
*/
export function cancel(data) {
    return request({
        url: 'purchase/recheckOrder/bindOrNotPurchaseRecheckOrderCancel',
        method: 'post',
        data: formData(data)
    })
}