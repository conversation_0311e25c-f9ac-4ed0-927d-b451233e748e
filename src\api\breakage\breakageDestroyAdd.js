import request from "@/utils/request";

//获取字典枚举值
export function getDictCode(query) {
    return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
    });
}

//获取销毁单号
export function getDestroyNo(data) {
    return request({
    url: "/warehouse/warehouseDestroy/applyDestroyNo",
    method: "post",
    data: data,
    });
}

//获取提取商品列表
export function getDestroyGoodsList(data) {
    return request({ 
    url: "/warehouse/breakage/pageQuery",
// "/warehouse/breakage/pageQuery", "/warehouse/warehouseCheck/overflowQuery"
    method: "post",
    data: data,
    });
}

//获取提取商品明细
export function getDestroyGoodsDetail(data) {
    return request({
    url: "/warehouse/warehouseCheck/overflowDetailQuery",
    method: "post",
    data: data,
    });
}

//新增销毁单
export function addDestroy(data) {
    return request({
    url:  '/warehouse/warehouseDestroy/save',
    //"/warehouse/warehouseCheck/saveOverflowing"
    method: "post",
    data: data,
    });
}

//提取报损单明细
export function getBreakageDetailList(data) {
    return request({
    url: "/warehouse/breakage/extract",
    method: "post",
    data: data,
    });
}