import BtnGroup from "./btn-group"; // 按钮组
import NoBtnGroup from "./btn-group-noreclick"; // 按钮组
import XyyPanel from "./xyy-panel"; // panel
import XyySwitch from "./xyy-switch"; // panel
import XyyDetail from "./xyy-detail-group"; // panel
import XyyDialog from "./xyy-dialog"; // 模态框
import TweenMessage from "./tweenMessage";
import FilterTableHead from "./filter-table-head"; // 筛选列
import XyyUpload from "./xyy-upload"; // 文件上传
import XyyButton from "./xyy-button";
import XyyImgUpload from "./xyy-img-upload"; // 图片上传
import XyyImgPreview from "./xyy-img-preview"; // 图片预览

const components = [
  BtnGroup,
  NoBtnGroup,
  XyyPanel,
  XyySwitch,
  XyyDetail,
  XyyDialog,
  TweenMessage,
  FilterTableHead,
  XyyUpload,
  XyyButton,
  XyyImgUpload,
  XyyImgPreview
];
const install = function (Vue, opts = {}) {
  components.forEach((component) => {
    Vue.component(component.name, component);
  });
};

export default {
  version: "1.0.0",
  install,
};
