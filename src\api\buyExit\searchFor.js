import request from "@/utils/request"

// 购进退出明细表查询
export function searchForOrderDetail(data) {
    return request({
        url: '/outstock/web/purchase/refundOrder/listOrderDetail',
        method: 'post',
        data
    })
}
// 购进退出明细表操作
export function searchForOrderDetailOperation(data) {
    return request({
        url: '/outstock/web/purchase/refundOrder/updateOrderStatusFromTempToFinish',
        method: 'post',
        data
    })
}
// 业主列表查询
export function getListOwners(data){
    return request({
        url: '/outstock/web/common/listOwner',
        method: 'post',
        data
    })
}
