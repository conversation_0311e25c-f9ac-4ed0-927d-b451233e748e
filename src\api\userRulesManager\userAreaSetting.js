import request from '@/utils/request'

export function userAreaPageUser(data) {
  return request({
    url: '/basicdata/system/user/list',
    method: 'post',
    data:data
  })
}

//员工区域配置-获取员工区域信息
export function userAreaGetUserAare(data) {
  return request({
    url: '/basicdata/userArea/getUserAare',
    method: 'post',
    data:data
  })
}

//员工区域配置-获取区域编码
export function userAreaAreaCode(data) {
  return request({
    url: '/basicdata/userArea/areaCode',
    method: 'post',
    data:data
  })
}

//员工区域配置-获取岗位
export function userAreaAreaPost(data) {
  return request({
    url: '/basicdata/system/post/list-all-simple',
    method: 'post',
    data:data
  })
}

//员工区域配置-新增区域配置
export function userAreaAreaAddUserAare(data) {
  return request({
    url: '/basicdata/userArea/addUserAare',
    method: 'post',
    data:data
  })
}




