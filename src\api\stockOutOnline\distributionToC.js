import request from '@/utils/request'

/**
 *   验证工号
 * @param {object} 
 */
 export function verificationJobNumber(data) {
    return request({
      url: '/outstock/web/common/checkJobNumber',
      method: 'get',
      params: data
    })
  }

/**
 *   分播位分配接口
 * @param {object} 
 */
 export function distributionOrder(data) {
  return request({
    url: '/outstock/web/outstock2c/distribution/allocation',
    method: 'post',
    data: data
  })
}

/**
 *   商品分播
 * @param {object} 
 */
 export function distributionProduct(data) {
  return request({
    url: '/outstock/web/outstock2c/distribution/distributionProduct',
    method: 'post',
    data: data
  })
}

/**
 *   分播订单详情
 * @param {object} 
 */
 export function distributionOrderDetail(data) {
  return request({
    url: '/outstock/web/outstock2c/distribution/distributionOrderDetail',
    method: 'POST',
    params: data
  })
}

/**
 *   分播完成
 * @param {object} 
 */
 export function distributionFinish(data) {
  return request({
    url: '/outstock/web/outstock2c/distribution/distributionFinish',
    method: 'post',
    data: data
  })
}

/**
 *   查询待分播任务数
 * @param {object} 
 */
 export function needDistributionTaskNumberCount(data) {
  return request({
    url: '/outstock/web/outstock2c/distribution/needDistributionTaskNumberCount',
    method: 'get',
    data: data
  })
}

/**
 *  面单打印
 */
export function printListExpressSheetPrint(data){
  return request({
    url: '/outstock/web/outstock2c/wave/print/listExpressSheetPrint',
    method: 'post',
    data: data
  })
}
