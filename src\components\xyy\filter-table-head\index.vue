<template>
  <div>
    <xyy-dialog ref="dialogTableVisible" :title="title" width="800px" @on-close="onClose">
      <xyy-panel :titleShow="false" v-if="title === '筛选列' && filterDone">
        <div v-table class="table-box">
          <vxe-table ref="xTable" border height="440" :data="columns" :row-key="true" @checkbox-change="changeCheckbox">
            <vxe-column type="checkbox" width="60"></vxe-column>
            <vxe-table-column type="seq" title="序号" width="60"></vxe-table-column>
            <vxe-table-column field="title" title="列名" min-width="200"></vxe-table-column>
            <vxe-table-column field="width" title="列宽" width="200"></vxe-table-column>
          </vxe-table>
        </div>
      </xyy-panel>
      <el-row v-else>
        <span v-for="(col, index) in columns" :key="index">
          <el-col v-if="!(col.type == 'seq' || col.type == 'checkbox')" :lg="6" :md="6" style="margin-bottom: 10px">
            <el-checkbox v-model="col.visible" :label="col.title" :disabled="col.disabled" />
          </el-col>
        </span>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetColumns" v-if="title === '筛选列' && filterDone">默认配置</el-button>
        <el-button @click="cancelBtnClick" icon="el-icon-close" type="info">取消</el-button>
        <el-button type="primary" @click="sureBtnClick" icon="el-icon-check">确定</el-button>
      </span>
    </xyy-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs';
export default {
  name: 'FilterTableHead',
  data() {
    return {
      // 筛选选项数据
      columns: [],
      title: '',
      oldColumns: [],
      filterDone: false,
      tableNo: ''
    }
  },
  created() { },
  methods: {
    /**
     * 打开筛选列/导出列弹窗
     * @param {Array} columns 列数据
     * @param {Number} type 弹窗类型 0：导出列 1：筛选列
     * @param {Boolean} filterDone 是否为自定义筛选列
     * @param {String} tableNo 表格编号，用于区分单页多表格重置功能
     */
    open(columns, type, filterDone, tableNo) {
      this.filterDone = filterDone || false
      this.tableNo = tableNo || ''
      this.columns = [].concat(columns)
      this.title = type === 0 ? '导出列' : '筛选列'
      this.$refs.dialogTableVisible.open()
      document.addEventListener('keydown', this.handleKeyDown)
      //渲染自定义筛选列表格数据
      if (this.title === '筛选列' && this.filterDone) {
        setTimeout(() => {
          this.columns.forEach(item => {
            this.$refs.xTable.setCheckboxRow(item, item.visible)
          })
          this.initSortable()
        }, 666)
      }
    },
    //自定义筛选列拖拽排序
    initSortable() {
      const el = this.$refs.xTable.$el.querySelector('.vxe-table--body tbody')
      Sortable.create(el, {
        onEnd: (evt) => {
          const { oldIndex, newIndex } = evt
          const movedItem = this.columns.splice(oldIndex, 1)[0]
          this.columns.splice(newIndex, 0, movedItem)
          this.updateSeq()
          this.columns.forEach(item => {
            this.$refs.xTable.setCheckboxRow(item, item.visible)
          })
        }
      })
    },
    // 更新列序号
    updateSeq() {
      this.columns.forEach((item, index) => {
        item.seq = index + 1
      })
      this.$refs.xTable.reloadData(this.columns)
    },
    // 关联外联表格列是否可见
    changeCheckbox({ row, checked }) {
      row.visible = checked
    },
    // 外联表格重置事件
    resetColumns() {
      this.$emit('reset', this.tableNo)
      this.$refs.dialogTableVisible.close()
    },
    cancelBtnClick() {
      this.$refs.dialogTableVisible.close()
      this.$emit('on-close')
    },
    onClose() {
      document.removeEventListener('keyDown', this.handleKeyDown)
      this.$emit('on-close')
    },
    sureBtnClick() {
      const param = {
        columns: this.columns.filter(el => !['seq', 'checkbox'].includes(el.type)), // 去除复选框和序号的列集合
        fullColumns: this.columns, // 所有列集合
        type: this.title === '导出列' ? 0 : 1, // 弹窗类型
        tableNo: this.tableNo // 表格编号
      }
      this.$emit('confirm', param)
      this.$refs.dialogTableVisible.close()
      // this.submitExportData()
    },
    // handleKeyDown(e) {
    //   const keyCode = e.keyCode
    //   switch (keyCode) {
    //     case 13:
    //       this.sureBtnClick()
    //       break
    //   }
    // }
  }
}
</script>

<style scoped>
.table-box {
  height: 444px !important;
}
</style>
