import request from '@/utils/request'

//根据字典类型获取字典集合
export function getDictionaries(data) {
  return request({
    url: '/basicdata/dictBases/getByDictType',
    method: 'get',
    params: data
  })
}
// 根据多个编码获取字典
export function getDictionariesLists(data) {
  return request({
    url: '/basicdata/dictBases/getByDictTypeList',
    method: 'post',
    data
  })
}
// 获取库别下拉信息
export function getAllStorageTypeLists(data) {
  return request({
    url: '/basicdata/dictBases/getAllStorageType',
    method: 'post',
    data
  })
}
// 商品批号货位库存分页查询
export function getGoodsAllocationProductsLists(data){
  return request({
    url: '/warehouse/stock/findGoodsAllocationProducts',
    method: 'post',
    data
  })
}

//库存查询
export function getInventoryLists(data){
  return request({
    url: '/warehouse/query/storage',
    method: 'post',
    data
  })
}
