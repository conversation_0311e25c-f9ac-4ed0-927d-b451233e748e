import request from '@/utils/request'
// import axios from 'axios'
/**
 *   回库上架单列表
 * @param {object} 查询实体
 */
export function getBackTaskList(data) {
  return request({
    url: '/outstock/web/outstock2c/back/getBackTaskList',
    method: 'post',
    data: data
  })
}

/**
 *   回库商品明细列表
 * @param {object} 查询实体
 */
export function getBackTaskDetailList(data) {
  return request({
    url: 'stockout/backStorage2B/getBackTaskDetailList',
    method: 'post',
    data: data
  })
}
/**
 *   回库确认
 * @param {object} 查询实体
 */
export function confirmTask(data) {
  return request({
    url: '/outstock/web/outstock2c/back/confirmTask',
    method: 'get',
    params: data
  })
}
/**
 *   打印
 * @param {object} 查询实体
 */
export function backTaskDetailListPrint(data) {
  return request({
    url: '/outstock/web/outstock2c/back/backTaskDetailListPrint',
    method: 'get',
    params: data
  })
}

//【复核回库】查询明细列表
export function getStorageBackDetailList(data) {
  return request({
      url: "/outstock/web/outstock/back/getBackTaskDetailList",
      method: "get",
      params: data
  })
}
