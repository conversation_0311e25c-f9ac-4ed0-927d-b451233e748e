import request from "@/utils/request";

/**
 * 入库作业-入库作业查询
 */
export function queryPushStockProcess(param) {
  return request({
    url: "/instock/jobQuery/findPage",
    method: "post",
    data: param,
  });
}
/**
 * 入库作业-采购入库接口日志
 */
export function getInterfaceLogList(data) {
  return request({
    url: "/instock/interfaceLog/findInterfaceList",
    method: "post",
    data,
  });
}
/**
 * 根据采购单号获取订单详情
 * @param {object} 查询实体
 */
export function getDetailByDjbh(data) {
  return request({
    url: "/instock/interfaceLog/findOrderDetailListByDjbh",
    method: "post",
    data,
  });
}
/**
 * 采购入库单下传【状态重置】
 * @param {object} 查询实体
 */
export function updateWmsFlgReset(data) {
  return request({
    url: "/instock/interfaceLog/updateWmflgReset",
    method: "post",
    data,
  });
}

/**
 * 采购入库【导出】
 * @param {object} 查询实体
 *  */
export function exportPushStockProcess(data) {
  return request({
    url: "/export/submitExportTask",
    method: "post",
    data,
  });
}
