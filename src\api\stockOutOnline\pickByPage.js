import request from "@/utils/request";

/**
 * 零货任务索取查询
 * @param {Object} params
 */
export function findWeightItemDetil(params) {
    return request({
      url: "/paperPicking/parts/queryTaskList",
      method: "post",
      data: params,
    });
}
/**
 * 零货任务列表汇总查询
 * @param {Object} params 
 * @returns 
 */
export function queryStatistics(params) {
  return request({
    url: "/paperPicking/parts/queryStatistics",
    method: "post",
    data: params,
  });
}

/**
 * 零货索取任务
 * @param {Object} params 
 * @returns 
 */
export function queryAskFor(params) {
  return request({
    url: "/paperPicking/parts/askFor",
    method: "post",
    data: params,
  });
}

/**
 * 拣货确认
 * @param {Object} params 
 * @returns 
 */
export function queryPickingConfirm(params) {
  return request({
    url: "/paperPicking/parts/pickingConfirm",
    method: "post",
    data: params,
  });
}

/**
 * 重新打印拣货单
 * @param {Object} params 
 * @returns 
 */
export function queryReprintPickingList(params) {
  return request({
    url: "/paperPicking/parts/reprintPickingList",
    method: "post",
    data: params,
  });
}
