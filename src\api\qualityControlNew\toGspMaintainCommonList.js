import request from "@/utils/request";

/**
 * 质量管理新-所属经营范围
 */
export function getStoreManage() {
  return request({
    url: "/basicdata/dictBases/childrenVOS",
    method: "post",
  });
}

/**
 * 质量管理新-药品养护
 */
export function queryGspMaintainListPage(data) {
  return request({
    url: "warehouse/quality/control/queryGspMaintainListPage",
    method: "post",
    data: data,
  });
}

// 质量管理新 - 收货记录
export function findReceiveGspList(data) {
  return request({
    url:"/instock/gspstatistic/purchaseReceive/findReceiveGspList",
    method:"post",
    data:data,
  })
}

//质量管理新 - 验收记录
export function findCheckGspList(data) {
  return request({
    url:"/instock/gspstatistic/purchaseCheck/findCheckGspList",
    method:"post",
    data:data,
  })
}

//质量管理新-入库记录
export function findStorageGspList(data){
  return request({
    url:'/instock/gspstatistic/purchaseStorage/findStorageGspList',
    method:"post",
    data:data,
  })
}

//质量管理新-采购退出记录
export function findProductRefundGspList(data){
   return request({
    url:'/outstock/web/purchase/refundOrder/findProductRefundGspList',
    method:'post',
    data:data,
   })
}

//质量管理新-销售出库复核记录
export function getGSPDrugsReviewRecord(data){
  return request({
    url:'/outstock/web/outstock/partsInReviewLayout/getGSPDrugsReviewRecord',
    method:'post',
    data:data,
  })
}
//GSP-销售退回验收列表分页查询
export function queryGspSalesreturnCheck(data){
  return request({
    url:'/instock/salesreturn/gspSalesreturn/queryGspSalesreturnCheck',
    method:'post',
    data:data,
  })
}
//GSP-销售退回收货列表分页查询
export function queryGspSalesreturnReceive(data){
  return request({
    url:'/instock/salesreturn/gspSalesreturn/queryGspSalesreturnReceive',
    method:'post',
    data:data,
  })
}