import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/basicdata/login',
    method: 'post',
    data:data
  })
}

export function getInfo() {
  return request({
    url: '/version',
    method: 'get',
  })
}

export function logout() {
  return request({
    url: '/basicdata/logout',
    method: 'post'
  })
}

export function orgSetCurrent(data) {
  return request({
    url: 'org/set/current',
    method: 'post',
    data: data
  })
}

export function sysFindDict(data) {
  return request({
    url: '/sys/find/dict',
    method: 'post',
    data: data
  })
}

//logoutDefault
export function logoutDefault(data) {
  return request({
    url: '/logoutDefault',
    method: 'post',
    data: data
  })
}
//logoutCustom
export function logoutCustom(data) {
  return request({
    url: '/logoutCustom',
    method: 'post',
    data: data
  })
}
