import request from "@/utils/request";

/**
 * 查询单据列表
 * @param {data} 请求体
 */
export function getList(data) {
  return request({
    url: "warehouse/breakageMonitor/getList",
    method: "post",
    data: data,
  });
}

/**
 * 查询单据详情列表
 * @param {data} 请求体
 */
export function getMonitorDeatils(data) {
  return request({
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    url: "warehouse/breakageMonitor/getMonitorDetails",
    method: "post",
    params: data,
  });
}

/**
 * 
 * 追溯码查询
 */
export function queryOrderDrugregulatorycode(data) {
  return request({
    url: "warehouse/breakageMonitor/queryOrderDrugregulatorycode",
    method: "post",
    data: data,
  });
}


/**
 * 
 * 追溯码录入
 */
export function addMonitor(data) {
  return request({
    url: "warehouse/breakageMonitor/breakageMonitor/addMonitor",
    method: "post",
    data: data,
  });
}



/**
 * 
 * 追溯码删除
 */
export function deleteCode(data) {
  return request({
    url: "warehouse/breakageMonitor/deleteCode",
    method: "post",
    data: data,
  });
}