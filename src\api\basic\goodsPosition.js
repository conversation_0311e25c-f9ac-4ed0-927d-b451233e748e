import request from '@/utils/request';
/************基础资料************* */
/**
 * 基础资料-货位档案-货位字典
 */

//分页查询货位列表
export function getGoodsPositionList(params) {
    return request({
        url: '/basicdata/goodsPosition/pageQuery',
        method: 'post',
        data: params
    })
}

//库别字段
export function getAllStorageType() {
    return request({
        url: '/basicdata/dictBases/getAllStorageType',
        method: 'post',
    })
}

//联动库别查询所有库房
export function getAllStorageRoomByType(param) {
    return request({
        url: '/basicdata/dictBases/getAllStorageRoomByType',
        method: 'post',
        data: param
    })
}

//查询库房库区列表
export function getStorageRoomAreaList(params) {
    return request({
        url: '/basicdata/storageRoomArea/getStorageRoomAreaList',
        method: 'post',
        data: params
    })
}

//修改逻辑区域
export function updateGoodsPosition(data) {
    return request({
        url: '/basicdata/goodsPosition/saveLogical',
        method: 'post',
        data: data
    })
}

//获取逻辑区域为字典表 LJQY枚举值
export function getLogicalArea(query) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: query
    })
}

//获取ABC分类
export function getABC(query) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: query
    })
}

//修改逻辑区域
export function saveLogical(data) {
    return request({
        url: '/basicdata/goodsPosition/saveLogical',
        method: 'post',
        data: data
    })
}

//货位锁定
export function lockGoodsPosition(data) {
    return request({
        url: '/basicdata/goodsPosition/lock',
        method: 'post',
        data: data
    })
}

//货位解锁
export function unlockGoodsPosition(data) {
    return request({
        url: '/basicdata/goodsPosition/unlock',
        method: 'post',
        data: data
    })
}

//货位保存
export function saveGoodsPosition(data) {
    return request({
        url: '/basicdata/goodsPosition/save',
        method: 'post',
        data: data
    })
}
