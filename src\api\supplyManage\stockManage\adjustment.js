import request from '@/utils/request'
// import axios from 'axios'
/**
 *   获取库存调整查询数据
 * @param {object} 查询实体
 */
export function getStockAdjust(data) {
  return request({
    url: '/consumables/consumablesSafeStock/findStockAdjustPage',
    method: 'post',
    data: data
  })
}
/**
 *   获取库存调整暂存数据
 * @param {object} 查询实体
 */
export function getChangeToTemporary(data) {
  return request({
    url: '/consumables/consumablesSafeStock/changeToTemporary',
    method: 'post',
    data: data
  })
}
/**
 *   获取库存提交数据
 * @param {object} 查询实体
 */
export function getChangeToWait(data) {
  return request({
    url: '/consumables/consumablesSafeStock/changeToWait',
    method: 'post',
    data: data
  })
}
/**
 *   获取库存调整导出
 * @param {object} 查询实体
 */
export function getExportStockAdjustExcel(data) {
  return request({
    url: '/consumablesSafeStock/exportStockAdjustExcel',
    method: 'post',
    data: data
  })
}

/**
 *  打印库存调整单
 * @param {object} 查询实体
 * */
export function getPrintStockAdjust(data) {
  return request({
    url: '/consumables/consumablesSafeStock/printStockAdjust',
    method: 'post',
    data: data
  })
}
