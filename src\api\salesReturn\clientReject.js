import request from '@/utils/request'
import utils from '@/utils'

/**
 * 查询字典
 * @param {String} dictType
 */
export function getDictListByType(dictType) {
  return request({
    url: '/basicdata/dictBases/getByDictTypeList',
    method: 'post',
    data: dictType
  })
}

/**
 * 查询业务类型列表
 * @param {data} 请求体
 */
export function getOwnerList(data) {
  return request({
    url: 'ownerChannel/getOwnerChannelVOList',
    method: 'post',
    params: data
  })
}

/**
 * 获取容器编号列表
 * @param {data} 请求体
 */
export function getContainerList(data) {
  return request({
    url: '/instock/common/pageContainer',
    method: 'post',
    data: data
  })
}

/**
 * 获取不合格品驳回列表
 * @param {data} 请求体
 */
export function getCheckUnqualifiedDetails(queryType, data) {
  let queryURL = ''
  switch (queryType) {
    case 1:
      queryURL = '/instock/salesreturn/check/findCheckUnqualifiedDetails'
      break
    case 2:
      queryURL = '/instock/salesreturn/review/findReviewUnqualifiedDetails'
      break
    // case 3:
    //   queryURL = 'salesreturnMinisterReview/findMinisterReviewUnqualifiedDetails'
    //   break
    default:
      queryURL = '/instock/salesreturn/check/findCheckUnqualifiedDetails'
  }
  return request({
    url: queryURL,
    method: 'post',
    data
  })
}

/**
 * 验收、复查、质量复查提交
 * @param {data} 提交数据
 */
export function submitData(queryType, data) {
  let queryURL = ''
  switch (queryType) {
    case 1:
      queryURL = '/instock/salesreturn/check/updateCheckSf'
      break
    case 2:
      queryURL = '/instock/salesreturn/review/updateReviewSf'
      break
    // case 3:
    //   queryURL = 'salesreturnMinisterReview/updateMinisterReviewSf'
    //   break
    default:
      queryURL = '/instock/salesreturn/check/updateCheckSf'
  }
  return request({
    url: queryURL,
    method: 'post',
    data
  })
}

/**
 * 销售退回 - 委托方不合格驳回页面 - 导出
 * @param queryType 当前页签编号
 * @param data 请求参数报文
 */
export function exportData(queryType, data) {
  let queryURL = ''
  switch (queryType) {
    case 1:
      queryURL = '/salesreturnCheck/exportCheckUnqualifiedDetailReport'
      break
    case 2:
      queryURL = '/salesreturnReview/exportReviewUnqualifiedDetailReport'
      break
    case 3:
      queryURL = '/salesreturnMinisterReview/exportMinisterReviewUnqualifiedDetailReport'
      break
    default:
      queryURL = '/salesreturnCheck/exportCheckUnqualifiedDetailReport'
  }
  return request({
    url: queryURL,
    method: 'post',
    data
  })
  // utils.downFileByForm(queryURL, 'post', data)
}

