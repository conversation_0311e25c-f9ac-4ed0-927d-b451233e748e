import request from "@/utils/request";

/**
 * 运费规则-保存/编辑接口
 * @param {Object} data
 */
export function freightRuleSave(params) {
  return request({
    url: "freight/rule/save",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-省市区地址联动
 * @param {Object} data
 */
export function dictGetNextAreaListByCode(params) {
  return request({
    url: "/dict/queryAllProvinceAndCity",
    method: "get",
    params,
  });
}

/**
 * 运费规则-承运商下拉查询
 * @param {Object} data
 */
export function logisticsProvidersQueryAll(params) {
  return request({
    url: "/outstock/web/outstock/express/listLogisticsProvider",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-通过承运商查询产品下拉
 * @param {Object} data
 */
export function logisticsProvidersQueryProduct(params) {
  return request({
    url: "/outstock/web/outstock/express/listLogisticsProviderProduct",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-列表
 * @param {Object} data
 */
export function freightRulePage(params) {
  return request({
    url: "/freight/rule/page",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-删除接口
 * @param {Object} data
 */
export function freightRuleDel(params) {
  return request({
    url: "/freight/rule/del",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-查看详情接口
 * @param {Object} data
 */
export function freightRuleQuery(params) {
  return request({
    url: "freight/rule/query",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-查看详情接口
 * @param {Object} data
 */
export function enableBatch(params) {
  return request({
    url: "freight/rule/enable/batch",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-承运商产品维护列表
 * @param {Object} data
 */
export function providersProductList(params) {
  return request({
    url: "logistics/providers/product/list",
    method: "post",
    data: params,
  });
}

/**
 * 运费规则-承运商产品附加参数编辑
 * @param {Object} data
 */
export function providersProductEdit(params) {
  return request({
    url: "logistics/providers/product/edit",
    method: "post",
    data: params,
  });
}

/**
 * 内复核-获取单据编号的承运商和产品编码
 * @param {Object} data
 */
export function findCarrier(params) {
  return request({
    url: "/outstock/web/outstock/salesorder/getCarrier",
    method: "post",
    data: params,
  });
}

//基础资料-货位容器档案-货位与关系维护-已绑定逻辑区商品查询
export function selectBindProductList(data) {
  return request({
    url: "/basicdata/product/logicalRegion/selectBindProductList",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-待绑定商品列表查询
export function selectWaitBindProductList(data) {
  return request({
    url: "/basicdata/product/logicalRegion/selectWaitBindProductList",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-商品已绑定逻辑区查询
export function selectProductBindLogicalRegion(data) {
  return request({
    url: "/basicdata/product/logicalRegion/selectProductBindLogicalRegion",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-按ABC查询库存上下限及逻辑区域
export function selectABCInventory(data) {
  return request({
    url: "/basicdata/product/logicalRegion/getStockAndLogicalRegion",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-保存或更新逻辑区域
export function saveOrUpdate(data) {
  return request({
    url: "/basicdata/product/logicalRegion/saveOrUpdate",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-保存或更新逻辑区域
export function selectProductLogicalRegions(data) {
  return request({
    url: "/basicdata/product/logicalRegion/selectProductLogicalRegions",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-保存或更新逻辑区域查询
export function selectLogicalRegion(data) {
  return request({
    url: "/basicdata/product/logicalRegion/selectProductBindLogicalRegion",
    method: "post",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-导出
export function exportGoods(data) {
  return request({
    url: "/basicdata/goodsPosition/export",
    method: "get",
    data: data,
  });
}

//基础资料-货位容器档案-货位与关系维护-导入
export function importGoods(data) {
  return request({
    url: "/basicdata/goodsPosition/import",
    method: "post",
    data: data,
  });
}
