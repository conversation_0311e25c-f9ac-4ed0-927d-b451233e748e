<template>
  <el-dialog
    :title="title"
    :visible="dialogVisible"
    :close-on-click-modal="false"
    :width="width"
    :before-close="handleClose"
    append-to-body
    @on-close="handleClose"
  >
    <el-upload
      ref="elUpload"
      class="xyy-upload clearfix"
      :headers="headers"
      :action="action"
      :accept="accept"
      :on-preview="handlePreview"
      :on-change="handleChange"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :auto-upload="false"
      :limit="limit"
      :multiple="multiple"
    >
      <el-button slot="trigger" size="small" type="primary" style="float:right">选取文件</el-button>
      <slot name="tip" />

    </el-upload>
    <slot />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog' // 拖拽指令
import { getToken } from '@/utils/auth.js';
export default {
  name: 'XyyUpload',
  directives: { elDragDialog },
  props: {
    title: { // 标题
      type: String,
      default: ''
    },
    width: { // 弹窗宽度
      type: String,
      default: '648px'
    },
    action: { // 上传路径
      type: String,
      default: ''
    },
    multiple: { // 是否多选
      type: Boolean,
      default: false
    },
    limit: { // 文件数量限制，默认100
      type: Number,
      default: 100
    },
    accept: { // 接收文件类型
      type: String,
      default: ''
    },
    autoUpload: { // 自动提交
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      headers: {
        TGC: getToken(),
        Sourcetype: "PC"
      },
    }
  },
  methods: {
    close() {
      this.dialogVisible = false
      this.$emit('on-close')
    },
    open() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('on-close')
    },
    // 上传文件
    submitUpload() {
      this.$refs.elUpload.submit()
    },
    // 清空文件
    clearFiles() {
      this.$refs.elUpload.clearFiles()
    },
    handleRemove(file, fileList) {
    //   console.log(file, fileList)
    },
    handlePreview(file) {
      // console.log(file)
    },
    // 文件改变时回调
    handleChange(file, fileList) {
      // console.log(file, fileList)
      this.$emit('on-change', file, fileList)
    },
    handleExceed(files, fileList) {
      if (this.limit === 1) {
        this.$set(fileList[0], 'raw', files[0])
        this.$set(fileList[0], 'name', files[0].name)
        this.clearFiles() // 清除文件
        this.$refs['elUpload'].handleStart(files[0]) // 选择文件后的赋值方法
      }
    },
    // 确定回调
    handleOk() {
      this.autoUpload && this.submitUpload()
      this.$emit('on-confirm')
    },
    // 上传接口成功回调
    handleSuccess(response, file, fileList) {
      this.$emit('on-success', response, file, fileList)
    }
  }
}
</script>

<style lang="scss" scoped>
.xyy-upload::v-deep .el-upload {
  float: right;
}
.xyy-upload::v-deep .el-upload-list {
  float: left;
}
.xyy-upload::v-deep .el-upload-list__item:first-child {
  margin-top: 0;
}

</style>
