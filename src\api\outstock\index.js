import request from '@/utils/request'
/****销售出库****/
/**
 * 销售出库管理-正常销售订单
 */
export function queryOutStockSalesOrder(param) {
    return request({
        url: '/outstock/web/outstock/salesorder/list',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-正常销售订单-破次下发
 */
export function wavePublish(param) {
    return request({
        url: '/outstock/web/outstock/wave/publish',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-正常销售订单-提升优先级
 */
export function upPriority(param) {
    return request({
        url: '/outstock/web/outstock/salesorder/upPriority',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-正常销售订单-提升优先级
 */
export function setAutoDistribution(param) {
    return request({
        url: '/outstock/web/outstock/salesorder/autoDistribution',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-出库订单
 */
export function queryOutStockSpecialOrder(param) {
    return request({
        url: '/outstock/web/outstock/special/salesorder/specialDeliveryOrder',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-复核出库订单
 */
export function queryOutStockCombinationOrder(param) {
    return request({
        url: '/outstock/web/outstock/special/salesorder/getOrderInfoSpecialOrder',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-复核出库订单-订单详情
 */
export function queryOutStockDetailOrder(param) {
    return request({
        url: '/outstock/web/outstock/salesorder/detail',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-复核出库订单-复核明细列表
 */
export function getOrderCodeInfoByOrderCode(param) {
    return request({
        url: '/outstock/web/outstock/special/partsgoodsallocation/getOrderCodeInfoByOrderCode',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-特殊出库订单-破次下发
 */
export function wavePublish2(param) {
    return request({
        url: '/outstock/web/outstock/special/wave/publish',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-特殊出库订单-破次下发
 */
export function rushRedBySpecialOrder(param) {
    return request({
        url: '/outstock/web/outstock/special/rushred/rushRedBySpecialOrder',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-特殊出库订单-复核确认
 */
export function reviewConfirm(param) {
    return request({
        url: '/outstock/web/outstock/special/salesorder/reviewSpecialOrder',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-调度管理-正常出库订单-获取设置项
 */
export function getSettings() {
    return request({
        url: '/outstock/web/outstock/wave/getSettings',
        method: 'post',
        data: {}
    })
}
/**
 * 销售出库管理-调度管理-正常出库订单-更新设置项
 */
export function updateSettings(param) {
    return request({
        url: '/outstock/web/outstock/wave/updateSettings',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-拣货管理-整件任务索取查询
 */
export function queryWholePickingList(param) {
    return request({
        url: '/outstock/web/outstock/whole/picking/list',
        method: 'post',
        data: param
    })
}
/**
 * 销售出库管理-拣货管理-整件任务索取查询-统计
 */
export function queryWholeTaskStatistics() {
    return request({
        url: '/outstock/web/outstock/whole/picking/wholeTaskStatistics',
        method: 'get',
        data: {}
    })
}
/**
 * 销售出库管理-拣货管理-整件任务索取
 */
export function queryWholeTask(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/claim',
        method: 'post',
        data: params
    })
}
/**
 * 销售出库管理-拣货管理-agv整件任务索取
 */
export function queryAgvTask(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/agvClaim',
        method: 'post',
        data: params
    })
}
/**
 * 补货作业-agv补货任务索取
 */
export function queryAgvSupplementTask(params) {
    return request({
        url: '/warehouse/replenish/agvAskForDownTask',
        method: 'post',
        data: params
    })
}
/**
 * 销售出库管理-拣货管理-整件任务索取-校验工号
 */
export function findEmpNumber(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/findEmpNumber',
        method: 'get',
        params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-获取整件任务列表
 */
export function queryWholeTaskList(params) {
    return request({
        url: '/outstock/web/outstock/whole/pickingSearch/findPickingTaskList',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-拣货管理-拣货任务查询——获取整件任务查询明细
 */
export function queryWholeTaskDetail(params) {
    return request({
        url: '/outstock/web/outstock/whole/pickingSearch/findBase',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-拣货确认
 */
export function confirmPicking(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/confirmationPick',
        method: 'post',
        data: params
    })
}


/**
 *  销售出库管理-拣货管理-整件任务查询-获取扫码任务
 */
export function queryScanTask(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/getScanTask',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-获取已扫信息
 */
export function queryScannedInfo(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/getScanCompleteData',
        method: 'get',
        params: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-删除追溯码
 */
export function deleteTraceCode(params) {
    return request({
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/outstock/web/outstock/whole/picking/deleteScanCode',
        method: 'post',
        params: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-扫码
 */
export function scanCode(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/scanCode',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-打印整件拣货单
 */
export function printPickingList(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/getPrint',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-拣货管理-整件任务查询-打印整件拣货标签
 */
export function printPickingLabel(params) {
    return request({
        url: '/outstock/web/outstock/whole/picking/listTagPrint',
        method: 'post',
        data: params
    })
}

/**
 *  销售出库管理-调度管理-销售订单——订单详情
 */
export function querySellOrderDetail(params) {
    return request({
        url: '/outstock/web/outstock/salesorder/detail',
        method: 'post',
        data: params
    })
}
// 获取订单池补货
export function queryOrderPool(params) {
    return request({
        url: '/outstock/web/outstock/wave/listReplenishmentProduct',
        method: 'post',
        data: params
    })
}
// 补货任务生成
export function replenishment(params) {
    return request({
        url: '/outstock/web/outstock/wave/createReplenishmentTask',
        method: 'post',
        data: params
    })
}
// 指定批号
export function designatedNumberList(params) {
    return request({
        url: '/outstock/web/outstock/wave/specify/specify',
        method: 'post',
        data: params
    })
}
/**
 * 拣货作业-整件任务索取-解锁
 */
export function unlockThisTask(params){
    return request({
        url: '/outstock/web/outstock/unlockLog/saveLog',
        method: 'post',
        data: params
    })
}