import request from "@/utils/request";

//统计查询—每日出库统计
export function stockoutDayStatisticsList(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/dailystats/list",
    method: "post",
    data
  });
}
//统计查询—出库内部差错
export function externalPickError(data){
  return request({
    url: "/outstock/web/outstock/exception/task/externalPickError",
    method: "post",
    data
  })
}
//统计查询—拣货统计查询
export function pickUpStatisticsQuery(data){
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/pickUpStatisticsQuery",
    method: "post",
    data
  })
}
//统计查询—内复核统计查询 
export function getPartsInReviewCount(data){
  return request({
    url: "/outstock/web/outstock/partsInReviewLayout/getPartsInReviewCount",
    method: "post",
    data
  })
}
//统计查询-计拣统计
export function pieceRateStatistics(data){
  return request({
    url: "/outstock/web/outstock/partPicking/pieceRateStatistics",
    method: "post",
    data
  })
}

//统计查询-订单跟踪统计查询
export function orderTrackingStatistics(data){
  return request({
    url: "/outstock/web/outstock/salesorder/query/orderTracking",
    method: "post",
    data
  })
}