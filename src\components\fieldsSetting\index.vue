<template>
  <div>
    <el-dialog
      :visible="status"
      :title="title"
      custom-class="user-group-box"
      top="0"
      @close="close"
    >
      <div class="user-content">
        <el-table
          ref="multipleTable"
          :data="colCache"
          default-expand-all
          border
          max-height="400"
          @selection-change="handleSelectionChange"
          @row-click="rowClick"
        >
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="55"></el-table-column>
          <el-table-column label="列名" align="center">
            <template slot-scope="{row}">
              <span>{{row.name}}</span>
            </template>
          </el-table-column>
          <el-table-column label="列宽" align="center">
            <template slot-scope="{row}">
              <span>{{row.width}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="defaultConfiguration">默认配置</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { apiSysSetAutoColumn } from "@/api/dictionaryQuery";
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "提示"
    },
    checked: {
      type: Array,
      default: function() {
        return [];
      }
    },
    allDatas: {
      type: Array,
      default: () => []
    },
    colCache: {
      type: Array,
      default: () => []
    },
    tablePage: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      selections: [] // 选中的
    };
  },
  watch: {
    status(val, old) {
      if (val) {
        this.initDatas();
      }
    }
  },
  mounted() {
    this.initDatas();
  },
  methods: {
    initDatas() {
      // 选中列
      this.$nextTick(() => {
        this.selections = this.checked || [];
        this.selections.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true);
        }, this);
      });
    },
    /**
     * 保存回调
     */
    save() {
      if (!this.selections.length) {
        this.$message.error("未有选中项，请重新选择");
        return;
      }
      const arr = [];
      this.selections.forEach(item => {
        arr.push({ field: item.index, width: item.width });
      });
      this.setSysSetAutoColumn(arr);
    },
    /**
     * 默认配置回调
     */
    defaultConfiguration() {
      const arr = [];
      this.allDatas.forEach(item => {
        arr.push({ field: item.index, width: item.width });
      });
      this.setSysSetAutoColumn(arr);
    },
    /*
      设置自定义列
    */
    setSysSetAutoColumn(arr) {
      const params = {
        page: this.tablePage,
        columns: arr
      };
      apiSysSetAutoColumn(params).then(res => {
        if (res.code !== 0) {
          this.$message.error(res.msg || "操作失败");
          return;
        }
        this.$message.success(res.msg || "操作成功");
        this.$emit("callback");
        this.close();
      });
    },
    handleSelectionChange(datas) {
      this.selections = datas;
    },
    /**
     * 选中行
     */
    rowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },
    /**
     * 关闭回调
     */
    close() {
      this.$refs.multipleTable.clearSelection();
      this.$emit("update:status", false);
    }
  }
};
</script>

<style lang="scss">
.el-dialog.user-group-box {
  width: 50%;
  height: 600px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 50px;
    padding: 15px 20px;
    background: #eeeeee;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
}
</style>
