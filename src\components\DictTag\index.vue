<template>
  <span>
    <template v-for="(dict, index) in options">
      <!-- Tag 样式 -->
      <el-tag
        v-if="dict.dictCode == value"
        :disable-transitions="true"
        :key="dict.dictCode"
        :index="index"
        :type="dict.dictCode != value ? 'info' : ''"
      >
        {{ dict.dictName }}
      </el-tag>
    </template>
  </span>
</template>

<script>
export default {
  name: "DictTag",
  props: {
    type: String,
    value: [Number, String, Boolean, Array],
    options: Array,
  },
};
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
