import request from "@/utils/request";

/**
 * 零货任务索取查询
 * @param {Object} params
 */
export function findWeightItemDetil(params) {
    return request({
      url: "/outstock/web/outstock/partPicking/paperPickingList",
      method: "post",
      data: params,
    });
}
/**
 * 零货任务列表汇总查询
 * @param {Object} params 
 * @returns 
 */
export function queryStatistics(params) {
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/statistics",
    method: "post",
    data: params,
  });
}

/**
 * 零货索取任务
 * @param {Object} params 
 * @returns 
 */
export function queryAskFor(params) {
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/askFor",
    method: "post",
    data: params,
  });
}

/**
 * 拣货确认
 * @param {Object} params 
 * @returns 
 */
export function queryPickingConfirm(params) {
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/pickingConfirm",
    method: "post",
    data: params,
  });
}

/**
 * 重新打印拣货单
 * @param {Object} params 
 * @returns 
 */
export function queryReprintPickingList(params) {
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/reprintPickingList",
    method: "post",
    data: params,
  });
}

/**
 * 拣货单工号反显
 * @param {Object} params
 * @returns
 * */
export function queryPickingListWork(params) {
  return request({
    url: "/outstock/web/common/checkJobNumber",
    method: "get",
    params,
  });
}

/**
 * 电子标签拣货-查询
 */
export function queryOnlineTagPick(params){
  return request({
    url: "/outstock/web/outstock/dpsPicking/queryDpsTaskList",
    method: "post",
    data:params,
  })
}
/**
 * 电子标签拣货-索取
 */
export function getOnlineTagAskFor(params){
  return request({
    url: "/outstock/web/outstock/dpsPicking/paperPickingList/askFor",
    method: "post",
    data:params,
  })
}