import request from '@/utils/request'

/**
 * 初始化页面数据
 * @param {data} 请求体
 */
export function getUrgedPageData(data) {
  return request({
    url: '/purchaseOrder/getUrgedPageData',
    method: 'post',
    data: data
  })
}
/**
 *  获取采购员信息
 * @param {object} 查询实体
 */
export function selectListSysPrivUser_new(data) {
  return request({
    url: '/purchase/common/selectListSysPrivUser_new',
    method: 'post',
    data: data
  })
}

/**
 * 查询
 * @param {data} 请求体
 */
export function findUrgedList(data) {
  return request({
    url: '/purchaseOrder/findUrgedList_new',
    method: 'post',
    data: data
  })
}
/**
 * 供应商弹窗
 * @param {data} 请求体
 */
export function selectListSupplier(data) {
  return request({
    url: '/purchase/common/selectListSupplier',
    method: 'post',
    data: data
  })
}
/**
 * 采购员弹窗
 * @param {data} 请求体
 */
export function selectListSysPrivUser(data) {
  return request({
    url: '/purchase/common/selectListSysPrivUser',
    method: 'post',
    data: data
  })
}
/**
 * 商品编码弹窗
 * @param {data} 请求体
 */
export function selectListProductBasePurchase(data) {
  return request({
    url: '/productBase/selectListProductBasePurchase',
    method: 'post',
    data: data
  })
}
/**
 *  列表导出
 * @param {object} 查询实体
 */
export function excel_new(params) {
  return request({
    url: '/purchaseOrder/urged/export/excel_new',
    method: 'post',
    data: params
  })
}
/**
 * 提交
 * @param {data} 请求体
 */
export function submitUrged(data) {
  return request({
    url: '/purchaseOrder/submitUrged',
    method: 'post',
    data: data
  })
}

/**
 *  模糊查询采购单
 * @param {data} 请求体
 */
export function getList4VagueCode_new(data) {
  return request({
    url: '/purchaseOrder/getList4VagueCode_new',
    method: 'post',
    data: data
  })
}
/**
 *  查询采购单信息
 * @param {data} 请求体
 */
export function findVo(data) {
  return request({
    url: '/purchaseOrder/findVo',
    method: 'post',
    data: data
  })
}
/**
 *  商品批号查询
 * @param {data} 请求体
 */
export function queryProductBatchCode_new(data) {
  return request({
    url: '/purchase/common/queryProductBatchCode_new',
    method: 'post',
    data: data
  })
}
/**
 * 导出
 * @param {data} 请求体
 */
export function excel(data) {
  return request({
    url: '/purchaseOrder/urged/export/excel',
    method: 'post',
    data: data
  })
}
