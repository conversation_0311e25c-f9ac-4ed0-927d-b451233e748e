import request from "@/utils/request";

//已生成盘点计划列表查询
export function selectCheckList(data) {
  return request({
    url: "/warehouse/warehouseCheck/selectCheckList",
    method: "post",
    data: data,
  });
}

//库别字段
export function getAllStorageType() {
  return request({
    url: "/basicdata/dictBases/getAllStorageType",
    method: "post",
  });
}

//联动库别查询所有库房
export function getAllStorageRoomByType(param) {
  return request({
    url: "/basicdata/dictBases/getAllStorageRoomByType",
    method: "post",
    data: param,
  });
}

//查询库房库区列表
export function getStorageRoomAreaList(params) {
  return request({
    url: "/basicdata/storageRoomArea/getStorageRoomAreaList",
    method: "post",
    data: params,
  });
}

//获取字典字段
export function getDictCode(query) {
  return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
  });
}

//盘点货位列表查询
export function getCheckPositionList(data) {
  return request({
    url: "/warehouse/warehouseCheck/selectCheckGoodsList",
    method: "post",
    data: data,
  });
}

//新增盘点计划
export function addCheckPlan(data) {
  return request({
    url: "/warehouse/warehouseCheck/addWarehouseCheck",
    method: "post",
    data: data,
  });
}

//获取巷道
export function getAllAisle(data) {
  return request({
    url: "/basicdata/goodsPosition/allRoadway",
    method: "post",
    data: data,
  });
}

//盘点计划单打印
export function printCheckPlan(data) {
  return request({
    url: "/warehouse/warehouseCheck/print",
    method: "post",
    data: data,
  });
}

/**
 *  取消盘点计划
 * @param {object} 查询实体
 */
export function cancelWarehouseCheck(data) {
  return request({
    url: "/warehouse/warehouseCheck/cancelTask",
    method: "post",
    data: data,
  });
}

/**
 *  10.执行复盘
 * @param {object} 查询实体
 */
export function replayCheckData(data) {
  return request({
    url: "/warehouse/warehouseCheck/replayCheckData",
    method: "post",
    data: data,
  });
}

/**
 *  04.盘点任务明细列表
 * @param {object} 查询实体
 */
export function selectCheckDetail(data) {
  return request({
    url: "/warehouse/warehouseCheck/detail",
    method: "post",
    data: data,
  });
}

/**
 *  执行盘点
 * @param {object} 查询实体
 */
export function executeWarehouseCheck(data) {
  return request({
    url: "/stockCheck/executeWarehouseCheck",
    method: "post",
    data: data,
  });
}

/**
 *  取消盘点计划
 * @param {object} 查询实体
 */
export function getCheckInfo(data) {
    return request({
      url: '/stockCheck/getCheckInfo',
      method: 'post',
      data: data
    })
  }

  //按导入货位创建盘点单
  export function createCheckPlanByPosition(data) {
    return request({
      url: '/warehouse/warehouseCheck/importWarehouseCheck',
      method: 'post',
      data: data
    })
  }
  //变更盘点人
  export function updateCheckUser(data) {
    return request({
      url: '/warehouse/warehouseCheck/updateCheckUser',
      method: 'post',
      params: data
    })
  }
