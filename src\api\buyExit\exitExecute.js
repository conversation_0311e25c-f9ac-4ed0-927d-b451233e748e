import request from "@/utils/request"

// 列表查询
export function exitExecutData(data) {
  return request({
    url: '/outstock/web/purchase/refundOrder/queryPerformDocument',
    method: 'post',
    data
  })
}
// 明细查询
export function exitExecuteDetailData(data) {
  return request({
    url: '/outstock/web/purchase/refundOrder/queryPerformDocumentDetail',
    method: 'post',
    data
  })
}
// 容器列表查询
export function exitGetContainerData(data) {
  return request({
    url: '/outstock/web/common/listContainer',
    method: 'post',
    data
  })
}
// 可用货位列表查询
export function exitGetLocationData(data) {
  return request({
    url: '/outstock/web/purchase/refundOrder/listGoodsAllocation',
    method: 'post',
    data
  })
}
// 执行提交
export function exitSubmitFinall(data) {
  return request({
    url: '/outstock/web/purchase/refundOrder/saveTaskAssignment',
    method: 'post',
    data
  })
}
// 收货地址
export function exitAddress(data){
  return request({
    url: '/outstock/web/common/listSupplierAddress',
    method: 'post',
    data
  })
}