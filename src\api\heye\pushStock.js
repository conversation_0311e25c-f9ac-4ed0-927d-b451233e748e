import request from '@/utils/request'
/**
 * 列表 入库管理-入库收货-列表数据
 * @param {Object} data
 */
export function apiGetPushStockDeliveryList(params) {
  return request({
      url: '/equityCard/purchase-order/detail/list',
      method: 'post',
      data: params
  })
}
/**
 * 列表 入库管理-入库收货-详情列表
 * @param {Object} data
 */
// export function apiGetPushStockDeliveryDetailList(params) {
//   return request({
//       url: '/warehouseMovement/tjk/page',
//       method: 'post',
//       data: params
//   })
// }
/**
 * 列表 入库管理-入库收货-采购入库保存
 * @param {Object} data
 */
export function apiPushStock(params) {
  return request({
      url: '/equityCard/purchase-order/in-storage',
      method: 'post',
      data: params
  })
}
/**
 * 列表 入库管理-入库收货-获取推荐货位
 * @param {Object} data
 */
export function apiGetPosition(params) {
  return request({
      url: '/equityCard/purchase-order/recommend-goods-allocation/list?productCode='+params.productCode,
      method: 'get',
      data: {}
  })
}
/**
 * 列表 入库管理-入库收货-上架单打印
 * @param {Object} data
 */
export function apiPrintPage(params) {
  return request({
      url: '/equityCard/purchase-order/print-storage-order-detail',
      method: 'post',
      data: params
  })
}

