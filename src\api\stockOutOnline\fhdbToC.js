import request from '@/utils/request'

/**
 *   复核打包-索取
 * @param {object} 
 */
 export function getPartsInReviewGoodsSalesInfo(data) {
    return request({
      url: 'outstock/stockout/inreview2b/getPartsInReviewGoodsSalesInfo',
      method: 'post',
      params: data
    })
}
/**
 *   复核打包-获取复核的商品
 * @param {object} 
 */
 export function listPartsInReviewGoodsView(data) {
    return request({
      url: 'outstock/stockout/inreview2b/listPartsInReviewGoodsView',
      method: 'post',
      params: data
    })
}
/**
 *   复核打包-任务明细
 * @param {object} 
 */
 export function getPartsInReviewTaskDetail(data) {
    return request({
      url: 'outstock/stockout/inreview2b/getPartsInReviewTaskDetail',
      method: 'post',
      params: data
    })
}

/**
 *   复核打包-保存箱码
 * @param {object} 
 * delete
 */
 export function saveBoxCode(data) {
    return request({
      url: 'outstock/stockout/inreview2b/saveBoxCode',
      method: 'post',
      params: data
    })
}
/**
 *   复核打包-复核/取消复核
 * @param {object} 
 */
 export function review(data) {
    return request({
      url: 'outstock/stockout/inreview2b/review',
      method: 'post',
      data: data
    })
}
/**
 *   复核打包-商品列表
 * @param {object} 
 */
 export function getPartsInReviewTask(data) {
    return request({
      url: '/stockout/inreview2b/listPartsInReviewGoodsView',
      method: 'get',
      params: data
    })
}

/**
 *   复核打包-弹窗任务列表
 * @param {object} 
 */
 export function getPartsInReviewTaskWindow(data) {
  return request({
    url: 'outstock/stockout/inreview2b/getPartsInReviewTask',
    method: 'post',
    data: data
  })
}

/**
 *   复核打包-复核确认
 * @param {object} 
 */
 export function reviewConfirm(data) {
  return request({
    url: 'outstock/stockout/inreview2b/reviewConfirm',
    method: 'post',
    params: data
  })
}

export function changeCarrier(data) {
  return request({
    url: "salesOrder/salesController/changeCarrier",
    method: "post",
    data: data,
  });
}