import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/basicdata/system/user/page',
    method: 'post',
    data: query
  })
}

// 获取用户精简信息列表
export function listSimpleUsers() {
  return request({
    url: '/basicdata/system/user/list-all-simple',
    method: 'post'
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/basicdata/system/user/get',
    method: 'post',
    data:{id:userId}
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/basicdata/system/user/create',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/basicdata/system/user/update',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/basicdata/system/user/delete',
    method: 'post',
    data:{id:userId}
  })
}

// 导出用户
export function exportUser(query) {
  return request({
    url: '/basicdata/system/user/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 用户密码重置
export function resetUserPwd(id, password) {
  const data = {
    id,
    password
  }
  return request({
    url: '/basicdata/system/user/update-password',
    method: 'post',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/basicdata/system/user/update-status',
    method: 'post',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/basicdata/system/user/profile/get',
    method: 'post'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/basicdata/system/user/profile/update',
    method: 'post',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/basicdata/system/user/profile/update-password',
    method: 'post',
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/basicdata/system/user/profile/update-avatar',
    method: 'post',
    data: data
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: '/basicdata/system/user/get-import-template',
    method: 'post',
    responseType: 'blob'
  })
}

/**
 * 商品资料-枚举接口
 */
export function queryGoodsinfoDictByType(param) {
  return request({
      url: '/basicdata/dictBases/getByDictTypeList',
      method: 'post',
      data: param
  })
}

