<template>
  <div>
    <el-card shadow="never" class="form-card">
      <el-form
        ref="form"
        :model="formData"
        label-width="100px"
        class="searchform"
      >
        <el-row :gutter="20">
          <el-col :span="8">
              <el-form-item label="客户名称：">
                {{ formData.clientName ?formData.clientName :'' }}
              </el-form-item>
          </el-col>
          <el-col :span="8">
              <el-form-item label="日期:">
                {{ formData.orderTime }}
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="收货地址:">
                {{ formData.shippingAddress }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <xyy-panel title="销售出库详情">
            <!--筛选列组件-->
            <div slot="tools" style="float:right;">
                <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
                    <svg aria-hidden="true"
                        style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
                        viewBox="0 0 1024 1024">
                        <!-- # + iconfont项目内symbol图标代码 -->
                        <use xlink:href="#icon-headxitongguanli" />
                    </svg>
                </el-button>
            </div>
            <div v-table class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="auto"
                    :data="detailData"  resizable @resizable-change="resizableChange" :columns="tableColumn"
                    :key="tableKey">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template v-for="item in tableColumn">
                        <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
                            :min-width="item.width">
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <!-- <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div> -->
        </xyy-panel>
        <!--筛选列组件导入-->
        <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
  </div>
</template>
<script>
import { majorClientsJobProgressMonitoringDetails} from '@/api/jobMonitor/largeCustomersMonitor.js'
import { queryDictBase } from "@/api/basic/public";
import { tableColumns } from './config.js';
import utils from '@/utils'
import dayjs from 'dayjs'
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
export default {
  name: 'largeCustomeresDtail',
  components: {},
  data() {
    return {
      detailData: [],
      loading: false,
      tableKey: Date.now(), //表格列刷新flag
      tableColumn: tableColumns(),
      oldTableColumn: JSON.parse(JSON.stringify(tableColumns())),
      formData: {
        clientName: "",//客户名称
        orderTime: "",//下单时间
        shippingAddress: "",//收货地址
      },
    };
  },
  activated() {
    this.$nextTick(()=>{
            utils.pageActivated()
        })
    this.formData = this.$route.query;
    this.queryCustomerDetailData();
    this.getColumWidth('largeCustomeresDtail', 'tableColumn', 'xTable')
  },
  methods: {
    //查询自定义列方法实现
        // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
        // column:所需要渲染的表格绑定的列数据
        // table: 所需要渲染的表格的 'ref'
        getColumWidth(page, column, table) {
            const params = {
                page: page,
            }
            queryUserCustomColumn(params).then(res => {
                const { code, msg, result } = res
                if (code === 0 && result) {
                    const columns = result
                    //出参只含field和width的匹配
                    columns.forEach(item => {
                        //更改对应column的
                        this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
                        //匹配后端所传列顺序
                        const foundItem = this[column].find(d => d.field === item.field)
                        if (foundItem) {
                            this[column].push(foundItem)
                            this[column].splice(this[column].indexOf(foundItem), 1)
                        }
                    })
                    this.storeDone = true //查询到列表格数据标识
                    this.tableKey = Date.now() //强制刷新表格列
                    // 若返回数据格式存在 field,title,visible,width
                    // this[column] = columns
                    // this.tableKey = Date.now() 
                    // this.storeDone = true
                } else {
                    this.storeDone = false
                }
            })
        },

        //保存/更新自定义列方法实现
        //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
        // column： 所需要保存的表格绑定的列数据
        setColumnWidth(page, column) {
            const columns = this[column]
            const params = {
                page: page,
                columns: columns
            }
            saveUserCustomColumn(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.$message.success(msg)
                } else {
                    this.$message.error(msg)
                }
            })
        },
        //监测拖动列宽变化方法
        resizableChange({ column }) {
          this.tableColumn[this.tableColumn.findIndex(item => item.title === column.title)].width = column.resizeWidth
        },
        //重置自定义列宽方法
        //每一个vxe-table单独实现
        resetFilterTableHead(tableNo) {
          this.oldTableColumn = JSON.parse(JSON.stringify((tableColumns())));
          this.tableColumn = [...this.oldTableColumn]
          this.tableKey = Date.now()
          this.setColumnWidth('largeCustomeresDtail', 'oldTableColumn')
          this.getColumWidth('largeCustomeresDtail', 'tableColumn', 'xTable')
        },
        //设置筛选列
        setingTableDataHander(index) {
          this.type = index;
          let columns = [];
          columns = JSON.parse(JSON.stringify((this.tableColumn)));
          this.$refs.filterDialog.open(columns, 1, true, 'xTable')
        },
        // 设置表头筛选列-子组件回传
        setFilterTableHead({ type, fullColumns, tableNo }) {
          this.tableColumn = [...fullColumns]
          this.tableKey = Date.now()
          this.setColumnWidth('largeCustomeresDtail', 'tableColumn')
          this.getColumWidth('largeCustomeresDtail', 'tableColumn', 'xTable')
        },
    queryCustomerDetailData() {
      this.loading = true;
      const params = { clientCode: this.$route.query.clientCode,orderTime: this.$route.query.orderTime ? dayjs(this.$route.query.orderTime).format('YYYY-MM-DD') : '',expressCode: this.$route.query.expressCode };
      majorClientsJobProgressMonitoringDetails(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          this.loading = false;
          this.detailData = result;
          this.formData.logisticsDistributionMode =
            result.logisticsDistributionMode;
          this.formData.logisticsProvidersCheckedList =
            result.logisticsProvidersCheckedList || [];
        }else {
          this.loading = false;
          this.$message.error(msg);
        }
      });
    },
    // 按钮权限
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    },
    goToBack(){
        this.$store.state.tagsView.visitedViews = this.$store.state.tagsView.visitedViews.filter(
        (item) => item.name !== "customerDetail"
      )
      this.$router.push({
        name: 'customer'
      })
    }
  },
};
</script>
<style scoped>
.form-card .side-right {
  display: flex;
  justify-content: flex-end;
}
</style>
