import request from '@/utils/request';

export function login(data) {
  return request({
    url: '/basicdata/login',
    method: 'post',
    data: data,
  });
}
export function logout() {
  return request({
    url: '/user/logout',
    method: 'get',
  });
}

//05.获取仓库下组织机构数据
export function getCurrent() {
  return request({
    url: '/basicdata/selectOwnWarehouses',
    method: 'post',
  });
}

//05.选择仓库下组织机构数据
export function selectWarehouse(data) {
  return request({
    url: '/basicdata/selectWarehouse',
    method: 'post',
    data:data
  });
}

//菜单查询
export function basicdataIndex(data) {
  return request({
    url: '/basicdata/index',
    method: 'post',
    data: data,
  });
}
