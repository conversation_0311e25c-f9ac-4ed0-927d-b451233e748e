import request from "@/utils/request";

//在库养护列表分页查询
export function getMaintainInWarehouseList(data) {
  return request({
    url: "warehouse/maintenance/queryMaintainInWarehouseListPage",
    method: "post",
    data: data,
  });
}

//在库养护计划详情
export function getDetailsByMaintainCode(data) {
  return request({
    url: "warehouse/maintenance/queryDetailsByMaintainCode",
    method: "post",
    data: data,
  });
}

//在库养护-保存当前页面信息
export function saveCurrentMaintainDetails(data) {
  return request({
    url: "warehouse/maintenance/saveCurrentMaintainDetails",
    method: "post",
    data: data,
  });
}

//最终保存所有养护商品明细
export function saveFinalMaintainDetails(data) {
  return request({
    url: "warehouse/maintenance/saveFinalMaintainDetails",
    method: "post",
    data: data,
  });
}

//  在库养护计划详情打印
export function getMaintainInWarehousePrint(data) {
  return request({
    url: "/warehouse/maintenance/maintainPrint ",
    method: "post",
    data: data,
  });
}
