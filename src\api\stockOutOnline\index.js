import request from '@/utils/request'
/**
 * 电商出库管理-订单信息
 */

export function getOrderList(params) {
    return request({
        url: '/outstock/web/outstock2c/salesorder/findAbnormalSalesOrderList',
        method: 'post',
        data: params
    })
}
/**
 * 电商出库管理-订单信息-获取用户选择的仓库名称
 *
 */
export function getSelectWarehouse() {
    return request({
      url: 'sysConfig/getSelectWarehouse',
      method: 'post'
    })
}
/**
 *  电商出库管理 - 电商调度 - 获取波次下发日志
 *
 */
export function getPublishLogList(params) {
    return request({
      url: '/outstock/web/outstock2c/wave/getWavePublishLogList',
      method: 'post',
      data: params
    })
  }