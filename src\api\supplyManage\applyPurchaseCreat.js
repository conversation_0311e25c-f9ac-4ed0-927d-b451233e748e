import request from '@/utils/request'
// ----------------------新建收货单接口---------------------------
/**
 *  获取验收执行页面信息
 * @param {object} 查询实体
 */
export function subscribe(data) {
  return request({
    url: '/consumables/subscribeOrder/insertSubscribeOrder',
    method: 'post',
    data: data
  })
}
/**
 *  获取新增物品请求
 * @param {object} 查询实体
 */
export function queryOrder(data) {
  return request({
    url: '/consumables/basicdata/queryConsumablesInfoVo',
    method: 'post',
    data: data
  })
}
/**
 *  查询物品列表请求
 * @param {object} 查询实体
 */
export function queryList(params) {
  return request({
    url: 'consumables/basicdata/queryList',
    method: 'post',
    data: params
  })
}
/**
 *  耗材管理->业务查询->出库管理-申领单创建通过耗材编码+oa编码查询可用库存
 * @param {object} 查询实体
 */
export function queryStockPage(params) {
  return request({
    url: '/consumables/consumablesStock/queryStock/page',
    method: 'post',
    data: params
  })
}
