import request from '@/utils/request'

/**
 * 查询追溯码
 * @param {data} 请求体
 */
export function getQueryOrderDrugregulatorycode(data) {
  return request({
    url: 'warehouse/breakageMonitor/queryOrderDrugregulatorycode',
    method: 'post',
    data: data
  })
}

/**
 * 删除追溯码扫码记录
 * @param {data} 请求体
 */
export function getDeleteOrderDrugregulatorycode(data) {
  return request({
    url: 'warehouse/breakageMonitor/deleteCode',
    method: 'post',
    data: data
  })
}

/**
 * 追溯码扫码录入
 * @param {data} 请求体
 */
export function getAddMonitor(data) {
  return request({
    url: 'warehouse/breakageMonitor/breakageMonitor/addMonitor',
    method: 'post',
    data: data
  })
}

/**
 * 解锁
 * @param {data} 请求体
 */
export function getCheckPermissionBystaffNum(data) {
  return request({
    url: '/instock/purchase/codeScanOrder/checkPermissionBystaffNum',
    method: 'post',
    data: data
  })
}
