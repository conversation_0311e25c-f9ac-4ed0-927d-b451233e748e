import request from '@/utils/request'

//po导入页面查询
export function poImportList(data) {
    return request({
        url: 'outstock/web/outstock/remarkPrint/queryRemarksList',
        method: 'post',
        data
    })
}

//po数据导入
export function poImport(data) {
    return request({
        url: 'outstock/web/outstock/remarkPrint/doUpload',
        method: 'post',
        data
    })
}

//po数据删除
export function poImportDelete(data) {
    return request({
        url: 'outstock/web/outstock/remarkPrint/deleteByIdList',
        method: 'post',
        data
    })
}