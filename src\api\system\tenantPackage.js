import request from '@/utils/request'

// 创建租户套餐
export function createTenantPackage(data) {
  return request({
    url: '/basicdata/system/tenant-package/create',
    method: 'post',
    data: data
  })
}

// 更新租户套餐
export function updateTenantPackage(data) {
  return request({
    url: '/basicdata/system/tenant-package/update',
    method: 'post',
    data: data
  })
}

// 删除租户套餐
export function deleteTenantPackage(id) {
  return request({
    url: '/basicdata/system/tenant-package/delete',
    method: 'post',
    data:{id:id}
  })
}

// 获得租户套餐
export function getTenantPackage(id) {
  return request({
    url: '/basicdata/system/tenant-package/get',
    method: 'post',
    data: {id:id}
  })
}

// 获得租户套餐分页
export function getTenantPackagePage(query) {
  return request({
    url: '/basicdata/system/tenant-package/page',
    method: 'post',
    data: query
  })
}

// 获取租户套餐精简信息列表
export function getTenantPackageList() {
  return request({
    url: '/basicdata/system/tenant-package/get-simple-list',
    method: 'post'
  })
}
