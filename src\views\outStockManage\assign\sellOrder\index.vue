<template>
  <div class="app-container">
    <xyy-panel title="正常销售订单">
      <!-- 按钮组 -->
      <btn-group slot="tools" :btn-list="btnList" />
      <el-form ref="form" :model="formData" label-width="90px" class="searchform">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="订单号">
              <el-input v-model="formData.codeFuzzy" placeholder="XSD/YBM单号/CKD单号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称">
              <el-input v-model="formData.clientName" placeholder="客户名称/编码/助记码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="查询日期">
              <el-date-picker v-model="formData.SubmissionTime" type="datetimerange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '18:00:00']"
                value-format="yyyy-MM-dd HH:mm:ss" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="订单状态">
              <el-select v-model="formData.orderStatus" clearable filterable>
                <el-option label="全部" value="" />
                <el-option v-for="item in orderStatus" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单取消">
              <el-select v-model="formData.orderCancel" clearable filterable>
                <el-option label="全部" value="" />
                <el-option v-for="item in orderCancel" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下发结果">
              <el-select v-model="formData.waveOrderException" clearable filterable>
                <el-option label="全部" value="" />
                <el-option v-for="item in waveOrderException" :key="item.value" :value="item.value"
                  :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户类型">
              <el-select v-model="formData.isMajorClients" clearable filterable>
                <el-option label="全部" :value="null" />
                <el-option v-for="item in customerType" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-button type="warning" icon="el-icon-top" v-if="getPermission('btn:wms:normalsellorder:riseHandler')"
          @click="riseHandler()">提升优先级</el-button>
        <!-- <el-button type="primary" icon="el-icon-edit">指定批号</el-button> -->
        <el-button type="danger" icon="el-icon-paperclip" v-if="getPermission('btn:wms:normalsellorder:blushDialog')"
          @click="openBlushDialog()">冲红</el-button>
        <el-button type="success" icon="el-icon-crop"
          v-if="getPermission('btn:wms:normalsellorder:setAutoDistributionHandler')"
          @click="setAutoDistributionHandler()">设为自配</el-button>
        <el-button type="primary" icon="el-icon-bottom-right"
          v-if="getPermission('btn:wms:normalsellorder:waveHangler')" @click="waveHangler()">波次下发</el-button>
        <el-button type="primary" icon="el-icon-circle-plus-outline"
          v-if="getPermission('btn:wms:normalsellorder:addOrederDialog')" @click="addOrederDialog()">订单池补货</el-button>
        <el-button type="warning" icon="el-icon-edit" v-if="getPermission('btn:wms:normalsellorder:designatedNumber')"
          @click="designatedNumber()">指定批号</el-button>
      </el-row>
    </xyy-panel>
    <xyy-panel title="销售订单列表">
      <!-- <div class="color-row">
        背景色：
        <div style="background-color: #db9a3d; margin-right: 5px; margin-bottom: 5px;">K A</div>
        <div style="background-color: rgb(111,138,60); margin-right: 5px; margin-bottom: 5px;">取 消</div>
        <div style="background-color: rgb(58,135,173); margin-right: 5px; margin-bottom: 5px;">调 拨</div>
        <div style="background-color: #ca5f4c; margin-right: 5px; margin-bottom: 5px;">药检缺失</div>
      </div> -->
      <div slot="tools" style="float:right;">
        <el-button class="setField" type="primary" @click="setingTableDataHander()">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
      </div>
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-hover-row :data="tableData"
          :seq-config="{ startIndex: (tablePage.pageNo - 1) * tablePage.pageSize }" :row-class-name="rowClassName" :row-style="rowStyle"
          resizable height="auto" @resizable-change="resizableChange" :columns="tableColumns" :key="tableKey"
          @cell-click="cellClick" @checkbox-change="checkboxChange">
          <vxe-column type="checkbox"  width="60"></vxe-column>
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in tableColumns">
            <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
              :min-width="item.width">
              <!-- 使用插槽 -->
              <template #default="{ row }">
                <!-- 判断当前字段是否为出库单号 -->
                <span v-if="item.field === 'orderCode'" class="detailLink" @click="detailHandler(row)">
                  {{ row[item.field] }}
                </span>
                <!-- 如果不是出库单号字段，则直接显示字段内容 -->
                <span v-else>{{ row[item.field] }}</span>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
      <div class="pager">
        <vxe-pager border :current-page="tablePage.pageNo" :page-sizes="tablePage.pageSizes" :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]" @page-change="handlePageChange" />
      </div>
    </xyy-panel>
    <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
    <setting-model ref="settingDialogVisible" @dialog-close="dialogClose" />
    <detail-dialog ref="detailDialog" @on-close="detailDialogClose" />
    <order-full-dialog ref="orderFullDialog" @order-close="orderDialogClose" />
    <!-- 冲红弹框 -->
    <blush-dialog ref="blushDialog" @blush-close="blushDialogClose" />
  </div>
</template>
<script>
import utils from '@/utils'
import { tableColumns } from "./config"
import settingModel from "./settingModel.vue"
import detailDialog from './components/detailDialog.vue'
import orderFullDialog from './components/orderFullDialog.vue'
import blushDialog from "./components/blushDialog.vue"
import XEUtils from 'xe-utils'
import { formatDateRange } from "@/utils/index.js"
import { queryOutStockSalesOrder, wavePublish, upPriority, setAutoDistribution, designatedNumberList } from "@/api/outstock/index"
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'

const end = new Date();
const start = XEUtils.getWhatDay(end, -7); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
  components: {
    settingModel,
    detailDialog,
    orderFullDialog,
    blushDialog
  },
  data() {
    return {
      btnList: [
        {
          label: '查询',
          type: 'primary',
          icon: 'el-icon-search',
          clickEvent: this.searchHandler,
          code: "btn:wms:normalsellorder:search",
        },
        // {
        //   label: '导出',
        //   type: 'success',
        //   icon: 'el-icon-download',
        //   clickEvent: this.getList,
        //   code: "btn:wms:normalsellorder:outExcel",
        // },
        {
          label: '设置',
          type: 'warning',
          icon: 'el-icon-setting',
          clickEvent: this.openSettingModal,
          code: "btn:wms:normalsellorder:setting",
        },
      ],
      tablePage: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        pageSizes: [50 ,100, 200, 300, 400],
      },
      // 默认时刻
      defaultDetailTime: ['00:00:00', '18:00:00'],
      tableData: [],
      tableColumns: tableColumns(),
      oldTableColumns: JSON.parse(JSON.stringify(tableColumns())), //原始列元素
      tableKey: Date.now(), //表格列刷新flag
      storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
      loading: false,
      formData: {
        clientName: "",
        codeFuzzy: "",
        orderStatus: "",
        isMajorClients: null, // 客户类型
        orderCancel: "",
        waveOrderException: "",
        makeInvoiceTimeSort: "",
        SubmissionTime: [`${defaultBeginTime} 00:00:00`, `${defaultEndTime} 18:00:00`], // 提交时间
      },
      waveOrderException: [
        {
          label: "失败",
          value: 1
        },
        {
          label: "成功",
          value: 0
        },
      ],
      orderCancel: [
        {
          label: "正常",
          value: 0
        },
        {
          label: "取消",
          value: 1
        },
      ],
      customerType: [
        {
          label: "非大客户",
          value: 0
        },
        {
          label: "大客户",
          value: 1
        },
      ],
      orderStatus: [
        {
          label: "初始",
          value: 0
        },
        {
          label: "待补货",
          value: 1
        },
        {
          label: "待生成拣货任务",
          value: 2
        },
        {
          label: "待绑墙",
          value: 3
        },
        {
          label: "待拣货索取",
          value: 4
        },
        {
          label: "拣货中",
          value: 5
        },
        {
          label: "拣货完成",
          value: 6
        },
        // {
        //     label:"复核中",
        //     value:7
        // },
        {
          label: "复核完成待称重",
          value: 8
        },
      ],
      // 时间限制
      // pickerOptions: {
      // disabledDate(time) {
      //   return time.getTime() > Date.now()
      // },
      // selectableRange: '00:00:00 - 23:59:59',
      // disabledDate: time => {
      //   const today = new Date();
      //   const endTime = new Date(this.formData.SubmissionTime[1]);
      //   return time.getTime() > endTime.getTime() || time.getTime() > today.getTime();
      // },
      // },
      currentRow: null,
      // 冲红选中的数据
      blushCheckedData: [],
      // 指定批号选中的数据
      designatedNumberData: []
    }
  },
  activated() {
    this.searchHandler()
    this.$nextTick(() => {
      this.getColumWidth('outstockassignsellorder', 'tableColumns', 'xTable')
      utils.pageActivated()
    })
  },
  methods: {
    checkboxChange({row}) {
      this.$refs.xTable.toggleCheckboxRow(row)
    },
    cellClick({row}){
      this.$refs.xTable.toggleCheckboxRow(row)
    },
    // 打开冲红弹框
    openBlushDialog() {
      this.blushCheckedData = this.$refs.xTable.getCheckboxRecords()
      if (this.blushCheckedData.length == 0) {
        this.$message.warning('请先选择一条(单选)需要冲红的订单')
      } else if (this.blushCheckedData.length != 1) {
        this.$message.warning('只能选择一条(单选)需要冲红的订单')
      } else {
        if (this.blushCheckedData[0].orderStatus == 100 && this.blushCheckedData[0].orderCancel == 0) {
          this.$refs.blushDialog.open(this.blushCheckedData)
        } else {
          this.$message.warning('只能冲红未下发的正常订单')
        }
      }
    },
    // 关闭冲红弹框执行的回调
    blushDialogClose() {

    },
    // 指定批号
    designatedNumber() {
      this.designatedNumberData = this.$refs.xTable.getCheckboxRecords()
      if (this.designatedNumberData.length != 1) {
        this.$message.warning('请先选择一条出库订单！')
      } else {
        if (this.designatedNumberData[0].orderCancel != 0) {
          this.$message.warning(`订单${this.designatedNumberData[0].erpOrderCode}已取消`)
        } else {
          if (this.designatedNumberData[0].orderStatus > 300) {
            this.$message.warning(`订单${this.designatedNumberData[0].erpOrderCode}已开始或已完成拣货`)
          } else {
            let params = {
              orderCodes: [this.designatedNumberData[0].orderCode]
            }
            designatedNumberList(params).then(res => {
              const { code, msg } = res
              if (code == 0) {
                this.$message.success('已将选择订单移入到指定批号下发列表')
                this.searchHandler()
              } else {
                this.$message.error(msg)
              }
            })
          }
        }
      }

    },
    /**权限控制 */
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map(item => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1
    },
    searchHandler() {
      this.tablePage.pageNo = 1
      this.queryOutStockSalesOrder()
    },
    queryOutStockSalesOrder() {
      this.loading = true
      const { pageNo, pageSize } = this.tablePage
      const [beginDate, endDate] = formatDateRange(this.formData.SubmissionTime)
      const params = Object.assign(
        { pageNo, pageSize },
        { beginDate: beginDate, endDate: endDate },
        this.formData)
      queryOutStockSalesOrder(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          this.tableData = result.result || []
          this.tablePage.pageNo = result.pageNo || 1
          this.tablePage.total = parseInt(result.totalCount)
        } else {
          this.tableData = []
          this.tablePage.pageNo = 1
          this.tablePage.total = 0
          this.$message.error(msg)
        }
        this.loading = false
      })
    },
    resetHandler() {

    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNo = currentPage
      this.tablePage.pageSize = pageSize
      this.queryOutStockSalesOrder()
    },
    //波次下发
    waveHangler() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length == 0) {
        this.$message.error("请选择数据")
        return
      }
      const orderCodes = selectedRows.map(item => item.orderCode);
      wavePublish({ orderCodes }).then(res => {
        const { code, msg } = res
        if (code == 0) {
          this.$message.success("操作成功")
        } else {
          this.$message.error(msg)
        }
        this.queryOutStockSalesOrder()
      })
    },
    // handleCurrentChange({row}){
    //     // 输出当前行数据
    //     this.currentRow = row
    //     console.log(row)
    // },
    // 提升优先级
    riseHandler() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length == 0) {
        this.$message.error("请选择数据")
        return
      } else if (selectedRows.length > 1) {
        this.$message.error("最多选择一条数据")
        return
      }
      upPriority({ orderCode: selectedRows[0].orderCode }).then(res => {
        const { code, msg } = res
        if (code == 0) {
          this.$message.success("操作成功")
          this.queryOutStockSalesOrder()
        } else {
          this.$message.error(msg)
        }
      })
    },
    rowStyle(row, rowlndex, $rowlndex){
      if(row.row.publishMessage &&  row.row.publishMessage.includes('预售商品锁定')){
        return {
          background: 'yellow',
          opacity: 0.8,
        }
      }    
    },
    rowClassName(row, rowlndex, $rowlndex) {
      // if (row.row.kaFlag > 0 && row.row.orderSort != 100) {
      //   return 'ka-row attach-lable' 
      // }
      // else if (row.row.orderCancel > 0  && row.row.orderSort != 100) {
      //   return 'cancle-row attach-lable'
      // }
      // else if (row.row.clientName.includes('小药药')  && row.row.orderSort != 100) {
      //   return 'self-row attach-lable'
      // }
      // else if (row.row.nrOfUnupload > 0  && row.row.orderSort != 100) {
      //   return 'noDrug-row attach-lable'
      // }
      // else if (row.row.kaFlag > 0 && row.row.orderSort == 100) {
      //   // console.log(row, 'row');
      //   return 'ka-row';
      // }
      // else if (row.row.orderCancel > 0 && row.row.orderSort == 100) {
      //   // console.log(row, 'row');
      //   return 'cancle-row';
      // }
      // else if (row.row.clientName.includes('小药药') > 0 && row.row.orderSort == 100) {
      //   // console.log(row, 'row');
      //   return 'self-row';
      // }
      // else if (row.row.nrOfUnupload > 0 && row.row.orderSort == 100) {
      //   // console.log(row, 'row');
      //   return 'noDrug-row';
      // }
      if (row.row.orderSort != 100) {
        return 'attach-lable'
      }
      else {
        return ''
      }
    },
    //设为自配
    setAutoDistributionHandler() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length == 0) {
        this.$message.error("请选择数据")
        return
      }
      const orderCodes = selectedRows.map(item => item.orderCode);
      setAutoDistribution({ orderCodes: orderCodes })
        .then(res => {
          const { code, msg } = res
          if (code == 0) {
            this.$message.success("操作成功")
            this.queryOutStockSalesOrder()
          } else {
            this.$message.error(msg)
          }
        })
        .catch(res => {
          this.$message.error(res.msg)
        })

    },
    //打开设置弹窗
    openSettingModal() {
      this.$refs.settingDialogVisible.open()
    },
    //弹窗关闭
    dialogClose() {

    },
    detailHandler(row) {
      this.$refs.detailDialog.open(row)
    },
    detailDialogClose() {

    },
    // 打开弹框
    addOrederDialog() {
      this.$refs.orderFullDialog.open()
    },
    // 订单池补货弹窗关闭
    orderDialogClose() {

    },
    //查询自定义列方法实现
    // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
    // column:所需要渲染的表格绑定的列数据
    // table: 所需要渲染的表格的 'ref'
    getColumWidth(page, column, table) {
      const params = {
        page: page,
      }
      queryUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          const columns = result
          //出参只含field和width的匹配
          columns.forEach(item => {
            //更改对应column的
            this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
            //匹配后端所传列顺序
            const foundItem = this[column].find(d => d.field === item.field)
            if (foundItem) {
              this[column].push(foundItem)
              this[column].splice(this[column].indexOf(foundItem), 1)
            }
          })
          this.storeDone = true //查询到列表格数据标识
          this.tableKey = Date.now() //强制刷新表格列
          // this.$nextTick(() => {
          //   this.$refs[table].refreshColumn()
          // })
          // 若返回数据格式存在 field,title,visible,width
          // this[column] = columns
          // this.tableKey = Date.now() 
          // this.storeDone = true
        } else {
          this.storeDone = false
        }
      })
    },

    //保存/更新自定义列方法实现
    //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
    // column： 所需要保存的表格绑定的列数据
    setColumnWidth(page, column) {
      // const columns = this[column].map(item => {
      //   return {
      //     field: item.field,
      //     width: item.width
      //   }
      // })
      const columns = this[column]
      const params = {
        page: page,
        columns: columns
      }
      saveUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },
    //监测拖动列宽变化方法
    resizableChange({ column }) {
      this.tableColumns[this.tableColumns.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },
    resetFilterTableHead() {
      this.oldTableColumns = JSON.parse(JSON.stringify((tableColumns())));
      this.tableColumns = [...this.oldTableColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('outstockassignsellorder', 'oldTableColumns')
      this.getColumWidth('outstockassignsellorder', 'tableColumns ', 'xTable')
    },
    setingTableDataHander(index) {
      // this.type = index;
      let columns = [];
      columns = JSON.parse(JSON.stringify((this.tableColumns)));
      this.$refs.filterDialog.open(columns, 1, true)
    },
    // 设置表头筛选列-子组件回传
    setFilterTableHead({ type, fullColumns }) {
      // for (let i = 0; i < this.columns.length; i++) {
      //    this.columns[i].visible = fullColumns[i].visible;
      // }
      // console.log(fullColumns,'full');

      this.tableColumns = [...fullColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('outstockassignsellorder', 'tableColumns')
      this.getColumWidth('outstockassignsellorder', 'tableColumns', 'xTable')
    },
  }
}
</script>
<style lang="scss" scoped>
:deep(.attach-lable) {
  background: url('../../../../assets/list_sort.png') no-repeat !important;
}

.detailLink {
  color: #337ab7;
  cursor: pointer;
}

.detailLink:hover {
  color: #163652;
}

// .color-row {
//   display: flex;
//   align-items: center;
//   justify-content: start;
// }

// :deep(.ka-row){
//     background-color:  rgb(219,154,61) !important;
// }

// :deep(.cancle-row){
//     background-color: rgb(111,138,60) !important;
// }

// :deep(.self-row){
//     background-color: rgb(58,135,173) !important;
// }

// :deep(.noDrug-row){
//     background-color: rgb(202,95,76) !important;
// }
</style>