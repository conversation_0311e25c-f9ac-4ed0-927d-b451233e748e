import request from '@/utils/request'
// ----------------------申购收货单接口---------------------------
/**
 *  获取可收货的申购单列表
 * @param {object} null
 */
export function findOrderUndoneList(data) {
  return request({
    url: '/consumables/subscribeOrderReceive/findOrderUndoneList',
    method: 'post',
    data: data
  })
}
/**
 *  获取申购单明细
 * @param {object} 申购单subscribeOrderId
 */
export function findOrderDetailList(data) {
  return request({
    url: '/consumables/subscribeOrderReceive/findOrderDetailList',
    method: 'post',
    data: data
  })
}
/**
 *  收货确认
 * @param {object} 数据实体
 */
export function recieveConfirm(data) {
  return request({
    url: '/consumables/subscribeOrderReceive/recieveConfirm',
    method: 'post',
    data: data
  })
}

/**
 *  关单
 * @param {object} 数据实体
 */
export function close(data) {
  return request({
    url: '/consumables/subscribeOrderReceive/close',
    method: 'post',
    data: data
  })
}

