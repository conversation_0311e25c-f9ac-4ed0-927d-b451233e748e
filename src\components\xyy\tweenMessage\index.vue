<template>
  <div class="bg" :style="{width:bgWidth,'background-color':bgcolor}">
    <span v-show="fristVisible" ref="spanFrist" :class="{move:secondVisible||fristMove,hide:!secondVisible&&!fristMove}" :style="{left:comFristX}" v-html="spanMessage" />
    <span v-show="secondVisible" ref="spanSecond" class="move" :style="{left:comSecondX}" v-html="spanMessage" />
    <div>
      <span v-if="!secondVisible&&!fristMove" class="dis" v-html="spanMessage" />
    </div>
  </div>

</template>

<script>
let setInterObj = ''
export default {
  name: 'TweenMessage',
  props: {
    message: {
      type: String,
      default: '' // 展示内容
    },
    speed: {
      type: Number,
      default: 0.5 // 速度
    },
    tweenwindth: {
      type: Number,
      default: 500 // 宽度
    },
    stopBoole: {
      type: Number,
      default: -1 // M默认-1  停止2  启动1
    },
    fristMove: {
      type: Boolean,
      default: false // 是否只滚动第一项无论 宽度是否满足
    },
    bgcolor: {
      type: String,
      default: '#FFFBE6'
    }
  },
  data() {
    return {
      fristVisible: true,
      secondVisible: false,
      fristX: 0,
      secondX: 0,
      startBoole: true
    }
  },
  computed: {
    spanMessage() {
      return this.message
    },
    speedCom() {
      return this.speed
    },
    comFristX() {
      return this.fristX + 'px'
    },
    comSecondX() {
      return this.secondX + 'px'
    },
    bgWidth() {
      return this.tweenwindth + 'px'
    }
  },
  watch: {
    message(val) {
      this.$nextTick(() => {
        this.startInit()
      })
    },
    stopBoole(val) {
      this.clearSetInterVal()
      if (val === 1) {
        this.tweenTimerEvent()
      }
    }
  },
  mounted() {
    this.startInit()
  },
  methods: {
    clearSetInterVal() {
      if (setInterObj) {
        clearInterval(setInterObj)
        setInterObj = ''
      }
    },
    startInit() {
      this.fristX = 0
      this.secondX = 0
      if (!this.$refs.spanFrist || !this.$refs.spanFrist.offsetWidth) {
        return false
      }
      if (this.$refs.spanFrist.offsetWidth >= this.tweenwindth) {
        this.startBoole = true
        this.secondVisible = true
        this.clearSetInterVal()
      } else {
        this.secondVisible = false
        this.startBoole = false
        if (this.fristMove) {
          this.fristMovetimerEvent()
        }
      }
      if (this.startBoole) { this.tweenTimerEvent(); this.startBoole = false }
    },
    fristMovetimerEvent() {
      if (setInterObj) {
        return false
      }
      setInterObj = setInterval(() => {
        this.fristX -= this.speed
        if (this.fristX <= -this.$refs.spanFrist.offsetWidth) {
          this.fristX = this.tweenwindth
        }
      }, 10)
    },
    tweenTimerEvent() {
      if (setInterObj) {
        return false
      }
      setInterObj = setInterval(() => {
        this.fristX -= this.speed
        this.secondX -= this.speed
        if (this.fristX <= -this.$refs.spanFrist.offsetWidth) {
          this.fristX = 0
        }
        if (this.secondX <= -this.$refs.spanSecond.offsetWidth) {
          this.secondX = 0
        }
      }, 10)
    },
    destroyed() {
      this.clearSetInterVal()
    }
  }
}
</script>
<style lang="scss" scoped>
.move{
  position: relative;
  display:inline-block;
  text-align: center;
}
.dis{
  display: block;
  text-align: center;
}
.hide{
  opacity:0;
  position: absolute;
}
.bg{
    overflow: hidden;      /*溢出隐藏*/
    white-space: nowrap;	/*规定文本不进行换行*/
}
</style>
