import request from "@/utils/request";

/**
 * 质量管理新-质量复查记录-库内
 */
export function findCheckRecordsNew(data) {
  return request({
    url: "warehouse/quality/control/findCheckRecordsNew",
    method: "post",
    data: data,
  });
}
//质量管理新-质量复查记录-入库
export function findRecheckGspList(data){
  return request({
    url:"/instock/gspstatistic/purchaseRecheck/findRecheckGspList",
    method:"post",
    data:data
  });
}
//GSP-销售退回-质量复查-销退
export function queryGspSalesreturnReview(data){
  return request({
    url:"/instock/salesreturn/gspSalesreturn/queryGspSalesreturnReview",
    method:"post",
    data:data
  })
}