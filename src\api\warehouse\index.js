import request from '@/utils/request'
import { formData } from '@/utils/index'
// ----------------------公共接口---------------------------
/**
 * 公共查询->用户模块->员工查询
 * @param {data} 请求体
 */
export function getListSysPrinUser(data) {
  return request({
    url: 'purchase/common/selectListSysPrivUser_new',
    method: 'post',
    data
  })
}
/**
 * 非公共接口-采购员查询
 * @param {data} 请求体
 */
export function getListPurchaseUser(data) {
  return request({
    url: '/instock/receiveOrder/queryBuyer',
    method: 'post',
    data
  })
}


/**
 * 获取供应商信息
 * @param {data} 请求体
 */
export function getListSupplier(data) {
  return request({
    url: '/instock/common/selectListSupplier',
    method: 'post',
    data
  })
}
/**
 *  获取商品选择列表
 * @param {object} 查询实体
 */
export function getProductList(data) {
  return request({
    url: '/instock/common/selectListProduct',
    method: 'post',
    data: data
  })
}

// ----------------------新建收货单接口---------------------------
/**
 * 获取页面信息
 * @param {object} 查询实体
 */
export function getPageInfo(data) {
  return request({
    url: 'purchase/receiveOrder/get',
    method: 'post',
    data: data
  })
}

/**
 * 获取供应商列表
 * @param {object} 查询实体
 */
export function getSupllaylist(data) {
  return request({
    url: 'purchase/common/selectListSupplier',
    method: 'get',
    params: data
  })
}
/**
 * 获取验收员列表
 * @param {object} 查询实体
 */
 export function selectListSysPrivUser(data) {
  return request({
    url: 'purchase/common/selectListSysPrivUser',
    method: 'get',
    params: data
  })
}

/**
 * 获取上架区域
 * @param {object} 查询实体
 */
export function getShelfArea(data) {
  return request({
    url: 'purchase/common/getStorageTypeArea',
    method: 'post',
    data: formData(data)
  })
}

/**
 * 批号模糊搜索
 * @param {object} 查询实体
 */
export function getBatchCode(data) {
  return request({
    url: '/instock/common/queryProductBatchCode',
    method: 'post',
    data: data
  })
}

/**
 * 拒收原因模糊搜索
 * @param {object} 查询实体
 */
export function getReceiveConclusion(data) {
  return request({
    url: '/instock/common/getDictByDictType',
    method: 'post',
    data: data
  })
}

/**
 * 有效期校验
 * @param {object} 查询实体
 */
export function validityConfigCheck(data) {
  return request({
    url: 'instock/common/productValidityConfigCheck',
    method: 'post',
    data: data
  })
}

/**
 * 批件校验
 * @param {object} 查询实体
 */
export function validityApprovalCheck(data) {
  return request({
    url: 'instock/common/productApprovalCheck',
    method: 'post',
    data: data
  })
}

/**
 * 获取容器列表
 * @param {object} 查询实体
 */
export function getContainerList(data) {
  return request({
    url: 'purchase/common/selectListContainer',
    method: 'get',
    params: data
  })
}

/**
 * 删除商品
 * @param {object} 查询实体
 */
export function delProduct(data) {
  return request({
    url: '/instock/receiveOrder/deleteDetailList',
    method: 'post',
    data: data
  })
}

/**
 * 取消商品
 * @param {object} 查询实体
 */
export function cancelProduct(data) {
  return request({
    url: '/instock/receiveOrder/delete',
    method: 'post',
    data: data
  })
}

/**
 * 商品维护
 * @param {object} 查询实体
 */
export function checkMaintainProduct(data) {
  return request({
    url: 'purchase/common/getVolumeAndDataStatus',
    method: 'get',
    params: data
  })
}

/**
 * 保存商品
 * @param {object} 实体
 */
export function submitProduct(data) {
  return request({
    url: 'purchase/receiveOrder/update',
    method: 'POST',
    data: data
  })
}
/**
 * 校验容器
 * @param {object} 查询实体
 */
export function checkContainer(data) {
  return request({
    url: 'purchase/common/checkContainer',
    method: 'get',
    params: data
  })
}

/**
 * 批量请求上架区域
 * @param {object} 查询实体
 */
export function getAllStorageTypeArea(data) {
  return request({
    url: 'purchase/common/getStorageTypeAreas',
    method: 'post',
    data: formData(data)
  })
}

// ----------------------采购入库接口日志---------------------------

/**
 * 采购入库接口日志列表
 * @param {object} 查询实体
 */
export function getInterfaceLogList(data) {
  return request({
    url: 'purchase/interfaceLog/findInterfaceList',
    method: 'post',
    data
  })
}

/**
 * 根据采购单号获取订单详情
 * @param {object} 查询实体
 */
export function getDetailByDjbh(data) {
  return request({
    url: 'purchase/interfaceLog/findOrderDetailListByDjbh',
    method: 'post',
    data
  })
}

/**
 * 采购入库单下传【状态重置】
 * @param {object} 查询实体
 */
export function updateWmsFlgReset(data) {
  return request({
    url: 'purchase/interfaceLog/updateWmflgReset',
    method: 'post',
    data
  })
}

// ----------------------收货-收货管理---------------------------
/**
 * 收货管理查询列表
 * @param {object} 查询实体
 */
export function getReceiveList(data) {
  return request({
    url: '/instock/receiveOrder/page_new',
    method: 'post',
    data
  })
}
/**
 * 批号下拉删除
 * @param {object} 
 */
export function deleteProductBatchCode(data) {
  return request({
    url: '/instock/common/deleteProductBatchCode',
    method: 'post',
    data
  })
}

/**
 * 收货管理查询列表
 * @param {object} 查询实体
 */
export function exportReceiveOrderList(data) {
  return request({
    url: 'purchase/receiveOrder/page/export_new',
    method: 'post',
    data
  })
}

/**
 * 收货单编号模糊查询
 * @param {object} 查询实体
 */
export function getList4VagueCodeAll(receiveOrderCode) {
  return request({
    url: 'purchase/receiveOrder/getReceiveOrderCodeList',
    method: 'post',
    data: { receiveOrderCode }
  })
}

// ----------------------拒收单---------------------------
/**
 * 拒收单查询列表
 * @param {object} 查询实体
 */
export function getRejectOrderList(data) {
  return request({
    url: '/instock/rejectOrder/queryList',
    method: 'post',
    data
  })
}

/**
 * 拒收单列表导出
 * @param {object} 查询实体
 */
export function exportRejectOrderList(data) {
  return request({
    url: 'purchase/rejectOrder/exportList',
    method: 'post',
    data
  })
}

/**
 * 拒收单完成
 * @param {object} 查询实体
 */
export function updateRejectOrderStatus(rejectOrderCode) {
  return request({
    url: '/instock/rejectOrder/updateRejectOrderStatus',
    method: 'post',
    data: { rejectOrderCode }
  })
}

/**
 * 拒收单编号列表查询
 * @param {object} 查询实体
 */
export function getRejectOrderAllList(rejectOrderCode) {
  return request({
    url: 'purchase/rejectOrder/queryRejectOrderCode',
    method: 'post',
    data: { rejectOrderCode }
  })
}

/**
 * 拒收单打印
 * @param {object} 查询实体
 * */
export function printRejectOrder(data) {
  return request({
    url: '/instock/rejectOrder/print ',
    method: 'post',
    data: data
  })
}


/**
 * 入库异常分页列表查询
 * @param {object} 查询实体
 */
export function getExceptionPage(param) {
  return request({
    url: '/instock/exception/page',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常-处理接口
 * @param {object} 查询实体
 */
export function exceptionDeal(param) {
  return request({
    url: '/instock/exception/deal',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常-图片查看接口
 * @param {object} 查询实体
 */
export function exceptionShowImgs(param) {
  return request({
    url: '/instock/exception/showImgs',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常分页列表导出
 * @param {object} 查询实体
 */
export function exceptionExportExcel(param) {
  return request({
    url: '/instock/exception/exportExcel',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常-删除
 * @param {object} 查询实体
 */
export function exceptionDelete(param) {
  return request({
    url: '/instock/exception/delete',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常-编辑
 * @param {object} 查询实体
 */
export function exceptionEdit(param) {
  return request({
    url: '/instock/exception/edit',
    method: 'post',
    data: param
  })
}

/**
 * 入库异常-消息回传神农
 * @param {object} 查询实体
 */
export function exceptionMqPush(param) {
  return request({
    url: '/instock/exception/mqPush',
    method: 'post',
    data: param
  })
}

// 库内-移出货位查询
export function goodsPositionListOut(param){
  return request({
    url: '/warehouse/locationMovement/findGoodsPositionList',
    method: 'post',
    data: param
  })
}

// 库内-移入货位查询
export function goodsPositionListIn(param){
  return request({
    url: '/warehouse/stock/recommendPosition',
    method: 'post',
    data: param
  })
}

/**
 * 库内-特价库移库单查询
 */
export function getSpecialPriceStorage(param){
  return request({
    url: '/warehouse/warehouseMovement/tjk/query',
    method: 'post',
    data: param
  })
}

/**
 * 库内-特价库图片预览
 */
export function drugTestReportPreview(param){
  return request({
    url: '/warehouse/warehouseMovement/tjk/queryTjkDetailById',
    method: 'post',
    data: param
  })
}

/**
 *库内-图片保存
 */
export function drugTestReportSave(params) {
  return request({
    url: "/warehouse/warehouseMovement/tjk/updateTjkDetailById",
    method: "post",
    data: params,
  });
}

/**
 * 库内-移库单审批
 */
export function saveCheckStatus(params){
  return request({
    url: "/warehouse/warehouseMovement/tjk/tjkApproval",
    method: "post",
    data: params,
  });
}
/**
 * 库内-移库单批量审批
 */
export function saveCheckStatusList(params){
  return request({
    url: "/warehouse/warehouseMovement/tjk/tjkApprovalList",
    method: "post",
    data: params,
  });
}