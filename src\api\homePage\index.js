import request from "@/utils/request"

//待出库数据统计
export function getWaitOutStockAmount(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/homePage/awaitOutstock",
    method: "post",
    data
  })
}
//待入库数据统计
export function getWaitInStockAmount(data) {
    return request({
      url: "/instock/homePage/pendingInventory",
      method: "post",
      data
    })
}
//待补货数据统计
export function getWaitReplenishingAmount(data) {
    return request({
      url: "/warehouse/homePage/awaitingReplenishment",
      method: "post",
      data
    })
}