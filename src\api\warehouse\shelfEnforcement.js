import request from '@/utils/request'

//联想查询上架区域/入库单编号
export function getShelfAreaList(data) {
    return request({
        url: '/instock/purchase/storageOrder/associativeStorageSearchList',
        method: 'post',
        data: data
    })
}

//模糊查询收货单
export function getReceiptList(data) {
    return request({
        url: '/instock/receiveOrder/getList4VagueCode',
        method: 'post',
        data: data
    })
}

//查询所有库房
export function getAllStorageRoomByType(param) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: param
    })
}

//查询库房库区列表
export function getStorageRoomAreaList(params) {
    return request({
        url: '/basicdata/storageRoomArea/getStorageRoomAreaList',
        method: 'post',
        data: params
    })
}

//获取上架单
export function getShelfList(data) {
    return request({
        url: '/instock/purchase/storageOrder/findList',
        method: 'post',
        data: data
    })
}

//商品上架明细列表
export function getShelfDetailList(data) {
    return request({
        url: '/instock/purchase/storageOrder/findDetailListByCodes',
        method: 'post',
        data: data
    })
}

//上架执行-拆分行
export function splitShelfDetail(data) {
    return request({
        url: '/instock/purchase/storageOrder/splitStorageOrderDetail',
        method: 'post',
        data: data
    })
}

//提交上架明细
export function submitShelfDetail(data) {
    return request({
        url: '/instock/purchase/storageOrder/executeStorageOrderDetail',
        method: 'post',
        data: data
    })
}

//AGV上架
export function agvSubmit(data){
    return request({
        url: '/instock/purchase/storageOrder/executeAgv',
        method: 'post',
        data: data
    })
}

//上架执行单-打印
export function printShelfOrder(data) {
    return request({
        url: '/instock/purchase/storageOrder/upShelfPrint',
        method: 'post',
        data: data
    })
}

//上架执行单-实际货位模糊查询
export function getShelfLocationList(data) {
    return request({
        url: '/basicdata/goodsPosition/getGoodsCodesByLike',
        method: 'post',
        data: data
    })
}

//上架执行单-批号模糊匹配
export function getBatchNumberList(data) {
    return request({
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/instock/wmsApp/purchase/storageOrder/getProductValidBatchCodes',
        method: 'post',
        params: data
    })
}

//上架执行单-批号联动日期
export function getBatchNumberDate(data) {
    return request({
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/instock/wmsApp/purchase/storageOrder/getProductValidBatchCodeInfo',
        method: 'post',
        params: data
    })
}

//上架执行单-商品扫码
export function getGoodsCodeList(data) {
    return request({
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/instock//wmsApp/purchase/storageOrder/selectProductPda',
        method: 'post',
        params: data  
    })
}

//上架执行单-商品明细批号是否模糊匹配
export function getBatchNumberIsLike(data) {
    return request({
        // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/basicdata/dictParam/getDictParamList',
        method: 'post',
        data: data
    })
}