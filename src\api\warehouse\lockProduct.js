import request from '@/utils/request'


// 获取业主下拉框信息
export function getOwnerSelect(param) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: param
    })
}

//分页查询锁定单列表
export function getLockProductList(param) {
    return request({
        url: '/warehouse/warehouseLockProduct/pageQuery',
        method: 'post',
        data: param
    })
}

//分页查询商品锁定单详情列表
export function getLockProductDetail(param) {
    return request({
        url: '/warehouse/warehouseLockProduct/pageQueryDetail',
        method: 'post',
        data: param
    })
}

//锁定新增页
export function getLockProductAdd(param) {
    return request({
        url: '/warehouse/warehouseLockProduct/lockProductAddView',
        method: 'post',
        data: param
    })
}

// 搜索商品锁定单详情列表
export function getLockProductDetailSearch(param) {
    return request({
        url: '/warehouse/stock/findGoodsAllocationProducts',
        method: 'post',
        data: param
    })
}

//查询锁定单原因下拉框
export function getLockProductReason(param) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: param
    })
}

//添加锁定单及商品明细
export function addLockProduct(param) {
    return request({
        url: '/warehouse/warehouseLockProduct/addWarehouseLockProductAndDetail',
        method: 'post',
        data: param
    })
}

//模糊查询商品编码
export function getGoodsCode(param) {
    return request({
        url: '/basicdata/master/productBase/queryByKeyword',
        method: 'post',
        data: param
    })
}