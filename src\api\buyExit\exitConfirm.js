import request from "@/utils/request"

// 购进退出单明细列表
export function buyExitDetailList(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/queryPerformDocument',
      method: 'post',
      data
    }
  )
}
// 购进退出拣货提交
export function exitConfirmSubmit(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/saveImplement',
      method: 'post',
      data
    }
  )
}
// 购进退出拣货打印
export function exitConfirmPrinter(pickUpOrder) {
  return request(
    {
      url: `/outstock/web/purchase/refundOrder/refundReviewOrderDetailPrint?pickUpOrder=${pickUpOrder}&printType=2`,
      method: 'get',
    }
  )
}
// 购进退出拣货明细接口
export function exitConfirmDetailData(data){
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/queryPerformDocumentDetail',
      method: 'post',
      data
    }
  )
}