<template>
  <div class="app-container" v-loading="mainLoading">
    <div class="el-card">
      <div class="el-card-left">
        <btn-group :btn-list="lbtnList" style="float:left;" />
      </div>
      <!-- 按钮组 -->
      <btn-group :btn-list="btnList" style="float:right;" />
      <div class="header-right" style="margin-right:10px;">
        <label>复核员：</label>
        <span id="t_name">{{ storageTool.getNickName() }}</span>
      </div>
    </div>
    <el-container>
      <!-- 左侧数据列表 -->
      <el-aside width="570px">
        <!-- table 组件 -->
        <vxe-table ref="reviewTable" height="960" class="review-table" :loading="loading" :data="tableData"
          :cell-class-name="cellClassName" @cell-click="cellClickRow" row-id="rowId" stripe resizable>
          <vxe-table-column type="seq" title="操作" fixed="left" width="120">
            <template v-slot="{ row }">
              <vxe-button v-if="row.reviewStatus === 0" status="primary" @click="reviewClick(row)">复核</vxe-button>
              <vxe-button v-else status="primary" @click="unReview(row)">取消复核</vxe-button>
            </template>
          </vxe-table-column>
          <vxe-table-column type="checkbox" width="40" />
          <vxe-table-column type="seq" title="序号" width="80" />
          <vxe-table-column field="productName" title="商品名称" width="120" />
          <vxe-table-column field="batchNumber" title="批号" width="200" />
          <vxe-table-column field="reviewNumberPieces" title="复核数量件数" width="180" />
          <vxe-table-column field="productCode" title="商品编码" width="200" />
          <vxe-table-column field="barCode" title="条形码" width="200" />
          <vxe-table-column field="reviewStatus" title="内复核状态" width="120" :formatter="reviewFormatter" />
          <vxe-table-column field="allocationCode" title="分配单号" width="200" />

          <vxe-table-column field="orderCode" title="出库单号" width="200" />
          <vxe-table-column field="erpOrderCode" title="销售单号" width="200" />
          <vxe-table-column field="packagingRequirement" title="包装要求" width="180" />
          <vxe-table-column field="productDateRequire" title="生产日期要求" width="120" />
          <vxe-table-column field="mnemonicCode" title="商品助记码" width="120" />
          <vxe-table-column field="specification" title="件包装" width="120" />
          <vxe-table-column field="reviewNumber" title="复核数量" width="120" />

          <vxe-table-column field="goodsAllocation" title="显示货位" width="120" />
          <vxe-table-column field="pickingNumber" title="计划零散数" width="120" />
          <vxe-table-column field="sterilizingBatchNumber" title="灭菌批号" width="200" />
          <vxe-table-column field="produceTime" title="生产日期" :formatter="timeFormatter" width="150" />
          <vxe-table-column field="validDate" title="有效期至" :formatter="timeFormatter" width="150" />
          <vxe-table-column field="realPickingNumber" title="实际数量" width="120" />
          <vxe-table-column field="realPickingNumberPieces" title="拣货数量件数" width="120" />
          <vxe-table-column field="whetherRegulatory" title="是否监管" width="120" :formatter="isFormatter" />
          <vxe-table-column field="acceptancePoints" title="验收注意点" width="120" />

          <vxe-table-column field="packingUnit" title="包装单位" width="120" />
          <vxe-table-column field="manufacturer" title="生产厂家" width="120" />
          <vxe-table-column field="specifications" title="规格" width="150" />
          <vxe-table-column field="producingArea" title="产地" width="120" />

          <vxe-table-column field="dosageFormName" title="剂型" width="120" />
          <vxe-table-column field="mediumPacking" title="中包装" width="120" />
          <vxe-table-column field="largePacking" title="件包装" width="120" />
          <vxe-table-column field="maxMediumPacking" title="最大中包装" width="120" />
          <vxe-table-column field="maxLargePacking" title="最大件包装" width="120" />
          <vxe-table-column field="isPrecious" title="是否贵重药品" width="120" :formatter="isFormatter" />
          <vxe-table-column field="isFragile" title="是否易碎" width="120" :formatter="isFormatter" />
          <vxe-table-column field="storageAttributes" title="商品特殊属性" width="120" />
          <vxe-table-column field="liquid" title="含有液体" width="120" :label-width="'82px'" />
        </vxe-table>
      </el-aside>
      <!-- 中间 商品信息详情 -->
      <el-main height="780px">
        <el-form ref="formData" :model="formData">
          <el-col :lg="24" :md="24">
            <el-form-item label="商品名称：" prop="productName" class="row">
              <span class="large-size">{{ formData.productName }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="规格：" prop="specifications">
              <span class="medium-size">{{ formData.specifications }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="批号：" prop="batchNumber" class="row">
              <span class="large-size">{{ formData.batchNumber }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="生产日期：" prop="produceTime">
              <span class="medium-size">{{ formData.produceTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="有效期至：" prop="validDate">
              <span class="medium-size">{{ formData.validDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="复核数量：" prop="reviewNumber">
              <span class="medium-size" style="color: red; font-size: 28px">{{
                formData.reviewNumber
              }}</span>
              <span class="medium-size">{{ formData.packingUnit }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="特殊属性：" prop="storageAttributes">
              <span class="medium-size">{{ formData.storageAttributes }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24">
            <el-form-item label="生产厂家：" prop="manufacturer" class="row">
              <span class="medium-size">{{ formData.manufacturer }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="16" :md="16">
            <el-form-item label="包装要求：" prop="packagingRequirement">
              <span class="medium-size">{{
                formData.packagingRequirement
              }}</span>
            </el-form-item>
            <el-form-item label="中包装数：" prop="mediumPacking" class="row">
              <span class="large-black-size">{{ formData.mediumPacking }}</span>
            </el-form-item>
            <el-form-item label="DI：" prop="di" class="row">
              <span class="large-black-size">{{ formData.di }}</span>
            </el-form-item>

            <div class="warning-class">{{ formData.storageAttributesExt }}</div>
            <div class="gift-class" v-if="formData.productGiftsName">{{ formData.productGiftsName }}：{{
              formData.productGiftsNumber }} {{ formData.giftsPackingUnit }}</div>
          </el-col>
          <!-- <el-col :lg="16" :md="16">
            <el-form-item label="商品重量:" prop="goodWeight" class="row">
              <span class="medium-size">{{ formData.goodWeight }}</span>
            </el-form-item>
          </el-col>
          <el-col :lg="16" :md="16">
            <el-form-item label="商品体积:" prop="goodVolume" class="row">
              <span class="medium-size">{{ formData.goodVolume }}</span>
            </el-form-item>
          </el-col> -->
          <el-col :lg="8" :md="8">
            <el-image :src="formData.src">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
          </el-col>
        </el-form>
      </el-main>
      <!-- 右侧 组件 -->
      <el-aside class="form-right" width="420px">
        <el-form :model="formRight" style="padding-top: 20px" :label-width="'82px'">
          <el-form-item label="销售单号：" prop="c">
            <el-input ref="erpOrderCode" :disabled="isSearchErp" v-model="formRight.erpOrderCode" placeholder="请输入销售单号"
              onfocus="this.select()" @keyup.enter.native="erpOrderCodeFunction" />
          </el-form-item>
          <!-- <el-form-item label="耗材码：" prop="boxCode">
            <el-input ref="boxCode" :disabled="addBoxCodeOpen === false" v-model="formRight.boxCode"
              placeholder="请输入耗材码" onfocus="this.select()" @keyup.enter.native="boxCodeFunction" />
          </el-form-item> -->
          <el-form-item label="耗材码：" prop="boxCode">
            <el-input ref="boxCode" v-model="formRight.boxCode" placeholder="请输入耗材码" onfocus="this.select()"
              @keyup.enter.native="boxCodeFunction" />
          </el-form-item>
          <el-form-item label="商品条码：" prop="barCode">
            <el-input ref="barCode" v-model="formRight.barCode" placeholder="请输入商品条码" onfocus="this.select()"
              @keyup.enter.native="barCodeEnter" />
          </el-form-item>
          <div class="row-carrierName">
            {{ formRight.carrierName }}
            <div style="color: red">{{ getAttributes() }}</div>
            <div style="color: red" v-if="isMajorClients">-大客户</div>
          </div>
          <!-- <div class="ul-list-container">
            <div class="ul-list">
              <div class="ul-list-item"></div>
              <div class="ul-list-item">总计</div>
              <div class="ul-list-item">已检：</div>
            </div>
            <div class="ul-list">
              <div class="ul-list-item">商品总数量</div>
              <div class="ul-list-item">{{ formRight.productNumberAll }}</div>
              <div class="ul-list-item">{{ formRight.productNumber }}</div>
            </div>
            <div class="ul-list">
              <div class="ul-list-item">品种数</div>
              <div class="ul-list-item">
                {{ formRight.inreviewProductNumberAll }}
              </div>
              <div class="ul-list-item">
                {{ formRight.inreviewProductNumber }}
              </div>
            </div>
            <div class="ul-list">
              <div class="ul-list-item">随货同行数</div>
              <div class="ul-list-item">{{ formRight.orderNumberAll }}</div>
              <div class="ul-list-item">{{ formRight.orderNumber }}</div>
            </div>
          </div> -->
          <vxe-table ref="lTable" height="167" :data="formRightArr">
            <vxe-table-column field="name"></vxe-table-column>
            <vxe-table-column field="totalValue" title="总计"></vxe-table-column>
            <vxe-table-column field="checkedValue" title="已检"></vxe-table-column>
          </vxe-table>
          <!-- 当前拼箱数 -->
          <div style="font-size: 25px; margin-top: 20px">
            {{ "拼箱数: " + formRight.consolidationNumber }}
          </div>
          <!-- 取消订单数数 -->
          <div style="font-size: 25px; margin-top: 20px">
            {{ "取消订单数: " + formRight.orderNumberCancelled }}
          </div>
          <!-- <div style="font-size: 25px; margin-top: 20px">
            {{ "商品总重量: " + formRight.totalWeight }}
          </div>
          <div style="font-size: 25px; margin-top: 20px">
            {{ "商品总体积: " + formRight.totalVolume }}
          </div> -->
        </el-form>
        <div v-table class="table-box">
          <div style="border-top: 1px solid lightgray; font-size: 22px; margin-top: 22px;">耗材</div>
          <vxe-table ref="rTable" :loading="boxLoading" height="233" :data="boxTableData">
            <vxe-table-column type="seq" title="序号" width="80" />
            <vxe-table-column field="boxCode" title="耗材码" min-width="100" />
            <vxe-table-column field="boxType" title="耗材名称" min-width="100" />
            <vxe-table-column field="operation" title="操作" width="95">
              <template #default="{ row }">
                <el-button type="text" @click="deleteBox(row)">删除</el-button>
              </template>
            </vxe-table-column>
          </vxe-table>
        </div>
      </el-aside>
    </el-container>

    <!-- 追溯码扫描 -->
    <tracing-code-scan ref="tracingCodeScanDialog" @on-before-close="closeDialog"
      @regulatoryCodeScanBack="changeTracing" />
    <!-- 查看任务弹窗 -->
    <view-tasks ref="viewTasksDialog" @on-before-close="closeViewTasksDialog" />
    <!-- 异常提交弹窗 -->
    <exception-submission ref="exceptionSubmitDialog" @on-before-close="closeExceptionSubmitDialog"
      @inputOpenDialog="openGoodsDialog(arguments)" @deleteRow="deleteRow" />
    <!-- 商品列表弹窗 -->
    <goods-dialog ref="goodsDialog" @checkGoodsData="checkGoodsData"></goods-dialog>
    <changecys ref="changecys" @back-data="changecysBackData"></changecys>
    <changeCYSAlertNew ref="dialogChangeCYSAlert"></changeCYSAlertNew>
    <editBox ref="editBox"></editBox>
  </div>
</template>

<script>
import { doTTS } from "@/utils/tts.js";
import tracingCodeScan from "./components/tracingCodeScan"; // 追溯码扫描
import editBox from "./components/editBoxCodeWindow.vue";
import changecys from "./components/changeCYSAlert.vue";
import changeCYSAlertNew from "./components/changeCYSAlertNew.vue";
import ViewTasks from "./components/viewTasks"; // 查看任务
import ExceptionSubmission from "./components/exceptionSubmission"; // 异常提交
import GoodsDialog from "./components/goodsDialog";   //商品列表弹窗
import SubmitAbnormal from "./components/submitAbnormal"; // 异常提交
import { modifyConsolidation } from "@/api/outstock/distribution";
import {
  getPartsInReviewGoodsSalesInfo,
  listPartsInReviewGoodsView,
  review,
  reviewConfirm,
  videoStart,
  videoPing,
  getMergeOrderList,
  deleteMergeOrder,
  checkParts,
  changeCarrier
} from "@/api/outstock/fhdb";
import XEUtils from "xe-utils"; // 表格组件工具
import Axios from "axios";
import { printNew } from '../../../utils/print';
export default {
  name: "ReviewPackage",
  components: {
    ExceptionSubmission,
    ViewTasks,
    // SubmitAbnormal,
    changecys,
    editBox,
    tracingCodeScan,
    GoodsDialog,
    changeCYSAlertNew
  },
  data() {
    return {
      boxLoading: false,
      intervalId: undefined,
      mainLoading: false,
      isSearchErp: false,
      btnList: [
        {
          label: "查看任务",
          type: "success",
          shortkey: "F4",
          icon: "el-icon-view",
          clickEvent: this.searchBtn,
          code: "btn:wms:reviewPackage:searchBtn",
        },
        {
          label: "复核确认",
          type: "success",
          shortkey: "F7",
          icon: "el-icon-check",
          clickEvent: this.reviewBtn,
          code: "btn:wms:reviewPackage:reviewBtn",
        },
        {
          label: "承运商更换",
          type: "warning",
          shortkey: "F10",
          icon: "el-icon-edit-outline",
          loading: false, // 设置按钮重复点击
          clickEvent: this.changeCYS,
          code: "btn:wms:reviewPackage:changeCYS",
        },
      ],
      lbtnList: [
        // {
        //   label: "增加箱码",
        //   type: "primary",
        //   //shortkey: "F2",
        //   icon: "el-icon-circle-plus-outline",
        //   clickEvent: this.addBoxCode,
        //   code: "btn:wms:reviewPackage:addBoxCode",
        // },
        // {
        //   label: "修改箱码",
        //   type: "warning",
        //   icon: "el-icon-edit-outline",
        //   clickEvent: this.editBoxCode,
        //   code: "btn:wms:reviewPackage:editBoxCode",
        // },
        {
          label: "异常提交",
          type: "warning",
          shortkey: "F1",
          icon: "el-icon-check",
          clickEvent: this.showExceptionSubmissionDialog,
          code: "btn:wms:reviewPackage:showExceptionSubmission",
        },
        {
          label: "刷新",
          type: "primary",
          shortkey: "F2",
          icon: "headshuaxin",
          clickEvent: this.refreshPage,
          code: "btn:wms:reviewPackage:refreshPage",
        },
      ],
      // 表单数据:作为主表单上传的数据
      formData: {
        productName: "", // 商品名称
        specifications: "", // 规格
        batchNumber: "", // 批号
        produceTime: "", // 生产日期
        validDate: "", // 有效期至
        reviewNumber: "", // 复核数量
        packingUnit: "", // 单位
        storageAttributes: "", // 特殊属性
        manufacturer: "", // 生产厂家
        packagingRequirement: "", // 包装要求
        mediumPacking: "", // 中包装数量
        storageAttributesExt: "", //存放属性扩展
        // goodWeight: '', // 商品重量
        // goodVolume: '', // 商品体积
        src: require("@/assets/images/product.png"),
        di:'',//DI码
      },
      checkRow: undefined,
      printStatus: false,
      formRight: {
        allocationCode: "", // 分配单号
        erpOrderCode: "", // 销售单号
        boxCode: "", // 耗材码
        consolidationCode: undefined, //拼箱号,如果有拼箱号，需要扫描耗材码
        orderCode: "", //出库单号
        barCode: "", // 商品条码
        productNumberAll: 0, // 商品总数量
        productNumber: 0, // 商品已检数量
        inreviewProductNumberAll: 0, // 品种数总数量
        inreviewProductNumber: 0, // 品种已检数
        supervisoryCodeAcquisition: "",
        orderNumberAll: 0,
        orderNumber: 0,
        consolidationNumber: 0, //当前拼箱数
        orderNumberCancelled: 0, //取消订单数
        carrierName: "", //承运商名称
        // totalWeight: 0,
        // totalVolume: 0,
      },
      isMajorClients: false, //是否大客户
      checkCancelOrder: 1,
      hotkeys: "F1,F2,F4,F7,F10", // 页面快捷键
      tableData: [], // 表格数据
      boxTableData: [], // 箱码表格数据
      hasDialog: false, // 弹窗禁止操作快捷键
      loading: false, // 表格加载层
      mergeOrderCode: "",
      addBoxCodeOpen: false,
      videoId: "-1",
      formRightArr: [],
    };
  },
  // mounted() {
  //   this.$refs.erpOrderCode.focus();
  // },
  activated() {
    this.$refs.erpOrderCode.focus();
  },
  created() {
    this.loading = true; // 加载中
    setTimeout(() => {
      this.loading = false;
    }, 1000);
    // 绑定快捷键
    this.$hotkeys(this.hotkeys, (event, handler) => {
      if (this.hasDialog) {
        return false;
      }
      event.preventDefault(); // 阻止浏览器默认行为
      switch (handler.key) {
        case "F1":
          this.showExceptionSubmissionDialog();
          break;
        case "F2":
          this.refreshPage();
        break;
        case "F4":
          this.searchBtn();
          break;
        case "F7":
          this.reviewBtn();
          break;
        case "F10":
          this.changeCYS()
      }
    });
  },
  beforeDestroy() {
    // 销毁快捷键
    this.$hotkeys.unbind(this.hotkeys);
    clearInterval(this.intervalId);
  },
  methods: {
    refreshPage(){
      this.clearPage();
    },
    getAttributes() {
      if (this.tableData.length === 0) {
        return "";
      }
      return this.tableData[0].attributes
        ? "(" + this.tableData[0].attributes + ")"
        : "";
    },
    //删除异常弹框的数据
    deleteRow(params) {
      this.$refs.goodsDialog.overWriteGoodsData(params)
    },
    //打开商品列表弹窗
    openGoodsDialog(params) {
      this.$refs.goodsDialog.open(params);
    },
    //子组件触发的事件
    checkGoodsData(params) {
      //调用异常提交组件方法
      this.$refs.exceptionSubmitDialog.receiveGoodsData(params);
    },
    editBoxCode() {
      this.$refs.editBox.open(this.mergeOrderCode);
    },
    addBoxCode() {
      this.addBoxCodeOpen = true;
      this.$nextTick(() => {
        this.formRight.barCode = "";
        this.$refs.boxCode.focus();
      });
    },
    cellClassName({ row, column }) {
      let rowName = "";
      if (row.reviewStatus == 2) {
        rowName = "cell-green";
      } else if (row.exceptionStatus !== null) {
        rowName = "cell-red";
      } else {
        rowName = "cell-white";
      }
      if (this.checkRow) {
        rowName = this.checkRow.barCode === row.barCode ? "cell-blue" : rowName;
      }
      return rowName;
    },
    clearPage() {
      this.isMajorClients = false; // 是否大客户
      this.isSearchErp = false;
      this.formRight = {
        allocationCode: "", // 分配单号
        erpOrderCode: "", // 销售单号
        boxCode: "", // 耗材码
        consolidationCode: undefined, //拼箱号,如果有拼箱号，需要扫描耗材码
        orderCode: "", //出库单号
        barCode: "", // 商品条码
        productNumberAll: 0, // 商品总数量
        productNumber: 0, // 商品已检数量
        inreviewProductNumberAll: 0, // 品种数总数量
        inreviewProductNumber: 0, // 品种已检数
        orderNumber: "",
        orderNumberAll: "",
        consolidationNumber: 0,
        orderNumberCancelled: 0,
        // totalWeight: 0,
        // totalVolume: 0,
      };
      this.formData = {
        productName: "", // 商品名称
        specifications: "", // 规格
        batchNumber: "", // 批号
        produceTime: "", // 生产日期
        validDate: "", // 有效期至
        reviewNumber: "", // 复核数量
        packingUnit: "", // 单位
        storageAttributes: "", // 特殊属性
        manufacturer: "", // 生产厂家
        packagingRequirement: "", // 包装要求
        mediumPacking: "", // 中包装数量
        storageAttributesExt: "", //存放属性扩展
        src: require("@/assets/images/product.png"),
        di:''//DI码
      };
      this.formRightArr = []
      this.tableData = [];
      this.boxTableData = []
      this.$nextTick(() => {
        this.$refs.erpOrderCode.focus();
      })
    },
    changecysBackData() {
      this.$refs.erpOrderCode.focus();
    },
    timeFormatter({ cellValue, row, column }) {
      return XEUtils.toDateString(cellValue, "yyyy-MM-dd");
    },
    reviewFormatter({ cellValue, row, column }) {
      let value = "";
      if (cellValue === 0) {
        value = "待复核";
      } else if (cellValue === 1) {
        value = "复核中";
      } else {
        value = "已复核";
      }
      return value;
    },
    isFormatter({ cellValue, row, column }) {
      let value = cellValue === 0 ? "否" : "是";
      return value;
    },
    changeCYS() {
      if (this.formRight.erpOrderCode === "") {
        this.$message.error("请输入销售单号");
        return;
      }
      changeCarrier({ orderCode: this.formRight.erpOrderCode }).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          this.$refs.dialogChangeCYSAlert.open(this.formRight.erpOrderCode, result)
        } else {
          this.$message.error(msg)
        }
      })
      //this.$refs.changecys.open(this.formRight.erpOrderCode);
    },
    // 单击选中行事件
    cellClickRow({ rowIndex, row }) {
      let obj = row;
      // console.log(row, "row");
      this.checkRow = row;
      this.formData = {
        productName: obj.productName, // 商品名称
        batchNumber: obj.batchNumber,//批号
        produceTime: obj.produceTime, // 生产日期
        validDate: obj.validDate, // 有效期至
        specifications: obj.specifications, // 规格
        reviewNumber: obj.reviewNumber, // 复核数量
        packingUnit: obj.packingUnit, // 单位
        storageAttributes: obj.storageAttributes, // 特殊属性
        manufacturer: obj.manufacturer, // 生产厂家
        packagingRequirement: obj.packagingRequirement, // 包装要求
        mediumPacking: obj.mediumPacking, // 中包装数量
        storageAttributesExt: obj.storageAttributesExt, //存放属性扩展
        productGiftsNumber: obj.productGiftsNumber,  //赠品数量
        productGiftsName: obj.productGiftsName,  //赠品名称
        giftsPackingUnit: obj.giftsPackingUnit,   //赠品单位
        // goodWeight: obj.goodWeight ? obj.goodWeight : "", // 商品重量
        // goodVolume: obj.goodVolume ? obj.goodVolume : "", // 商品体积
        src:
          obj.productPic
            ? obj.productPic[0]
            : require("@/assets/images/product.png"),
        di: obj.di,   //DI码
      };
      console.log(this.formData)
      this.setReviewDetails(obj, true);
    },
    // 列复核事件
    reviewClick(row) {
      // if (this.boxTableData.length == 0) {
      //   this.$message.error("请先输入耗材码后再进行操作");
      //   return;
      // }
      // console.log(row, "row");

      const { reviewStatus, whetherRegulatory } = row;
      if (reviewStatus === 2) {
        this.unReview(row);
      } else if (
        whetherRegulatory === 1 && row.reviewNumber != 0
      ) {
        this.whetherRegulatory(row); // 是否展示电子监管码弹窗
      } else {
        this.checkAndPosition1(row); // 选中相同商品复核（相同编码）
      }
      // if( whetherRegulatory === 1) {

      // }
    },
    // 取消复核
    unReview(row) {
      // if (this.boxTableData.length == 0) {
      //   this.$message.error("请先输入耗材码后再进行操作");
      //   return;
      // }
      this.apireview(row.allocationCode, row.productCode, 1, row.orderCode);
    },

    // 双击选中行并定位 \ 选中相同商品(同编码)
    checkAndPosition1(row) {
      // 商品编码的隐藏值
      const { allocationCode, productCode, orderCode } = row;
      if (productCode === "") {
        return;
      }
      // 获取所有商品行
      const tableRow = this.tableData;
      const ids = [];
      let num = 0;
      tableRow.forEach((i, v) => {
        if (productCode === i.productCode) {
          /**
           * setCheckboxRow(rows, checked)用于 type=checkbox，设置行为选中状态，
           * 第二个参数为选中与否rows: Row | Array<Row>, checked: boolean
           * */
          // 设置选中行
          this.$refs.reviewTable.setCheckboxRow(i, true);
          const id = i.id;
          num += i.reviewNumber;
          ids.push(id);
        }
      });
      const params = {
        productCode: productCode, // 商品编码
        orderCode: orderCode,
        mergeOrderCode: this.mergeOrderCode,
      };
      // 商品复核请求
      review(params).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.$message.success(msg);
          this.setRightNumber(result);
          this.apilistPartsInReviewGoodsView(); // 刷新列表
          this.dottsFunction(num + row.packingUnit);
        } else {
          this.$message.error(msg);
        }
      });
      // 如果id长度超过表格，设置滚动条
      if (ids.length) {
        this.scrollTop(row, productCode);
      }
    },

    //销售单号
    erpOrderCodeFunction() {
      this.apigetPartsInReviewGoodsSalesInfo();
    },
    //耗材号
    boxCodeFunction() {
      // this.apisaveBoxCode();
      this.apiCheckBoxCode();
    },

    // 监管码扫描成功后
    changeTracing(result) {
      this.setRightNumber(result);
      this.apilistPartsInReviewGoodsView(); // 刷新列表
    },
    // 商品条码回车事件
    barCodeEnter() {
      // if (this.boxTableData.length == 0) {
      //   this.$message.error("请先输入耗材码后再进行操作");
      //   return;
      // }
      const barCode = this.formRight.barCode;
      // 需要扫描电子监管码的复核商品
      const goodsList = [];
      let rowobj = {};
      let offon = 0;
      // 循环判断商品列表
      const table_data = this.tableData;
      const barCodeGoods = this.tableData.filter((item) => (barCode === item.di || item.barCode === barCode) );
      // const hasDuplicates = barCodeGoods.filter((item, index, self) => self.findIndex(
      //   t => t.productCode === item.productCode
      // ) !== index).length > 0
      // console.log(barCodeGoods.filter((item, index, self) => self.findIndex(
      //   t => t.productCode === item.productCode
      // ) !== index), 'where');

      const barCodeMap = new Map();
      const duplicateBarCodes = new Set();

      barCodeGoods.forEach(product => {
        const { barCode, productCode } = product;
        if (!barCodeMap.has(barCode)) {
          barCodeMap.set(barCode, new Set([productCode]));
        } else {
          const codes = barCodeMap.get(barCode);
          codes.add(productCode);
        if (codes.size > 1) {
            duplicateBarCodes.add(barCode);
          }
        }
      });

      // console.log(barCodeMap, 'barCodeMap');
      // console.log(duplicateBarCodes, 'duplicateBarCodes');

      // if (hasDuplicates) {
      //   this.$nextTick(() => {
      //     this.$alert('商品有多个编码，请仔细复核', '提示', {
      //     confirmButtonText: '确定',
      //     // cancelButtonText: '取消',
      //     type: 'warning',
      //   }).then(() => {
      //     return true
      //   })
      //   })
      // } else {


      // console.log(duplicateBarCodes, 'hasDuplicates');
      
      if (duplicateBarCodes.size > 0 && this.tableData.length > 0) {
        this.$nextTick(() => {
          this.$alert('商品有多个编码，请手动点击复核', '提示', {
          confirmButtonText: '确定',
          // cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          return true
        })
        })
      } else {
      for (var i = 0; i < table_data.length; i++) {
        const obj = table_data[i];
        var barCodeArr = obj.barCode ? obj.barCode.split(",") : [];

        for (var j = 0; j < barCodeArr.length; j++) {
          if (barCode === barCodeArr[j] || barCode === table_data[i].di) {
            if (obj.reviewStatus === 2) {
              this.setReviewDetails(obj); // 设置展示商品主信息
              this.$message.warning("该条形码对应的商品已复核！");
              this.$refs.barCode.select(); // 商品条码获取焦点
              this.dottsFunction("商品已复核");
              return;
            }
            // 是否需要扫描电子监管码whetherRegulatory值0为否 ，1为是
            if (obj.whetherRegulatory === 1) {
              if (obj.reviewNumber > 0) {
                offon = 1;
                goodsList.push(obj);
              }
            } else {
              this.$refs.barCode.select(); // 商品条码获取焦点
              rowobj = obj;
            }
          }
        }
      }
      if (Object.keys(rowobj).length !== 0) {
        this.setReviewDetails(rowobj); // 设置展示商品主信息
      }

      if (offon === 1) {

        let obj = goodsList[0];
        this.setReviewDetails(obj);
        const params = {
          allocationCode: obj.allocationCode,
          productCode: obj.productCode,
          goods: goodsList,
          orderCode: obj.orderCode,
          mergeOrderCode: this.mergeOrderCode,
        };
        this.$refs.tracingCodeScanDialog.open(params); // 是否展示电子监管码弹窗
        this.hasDialog = true;
        return;
      }
        if (Object.keys(rowobj).length !== 0) {
          this.checkAndPosition(rowobj, true);
          return
        } else {
          this.checkAndPosition();
        }
      }

      // this.$message.warning("找不到该条形码对应的商品！");
      // 清空商品中间已复核信息
      this.$refs.formData.resetFields();
      this.formData.packingUnit = "";
      this.formData.src = require("@/assets/images/product.png");
    },

    // 是否展示电子监管码弹窗
    whetherRegulatory(row) {
      const { allocationCode, productCode, whetherRegulatory, orderCode, largeCategoryCode } = row;
      // 需要扫描电子监管码的复核商品
      const goodsList = [];
      this.tableData.forEach((res) => {
        if (res.productCode === productCode) {
          if (
            res.exceptionCause != null &&
            res.exceptionReason != undefined &&
            res.exceptionNumber != null
          ) {
            res.reviewStatus = 0;
            return false;
          }
          if (res.reviewNumber != 0) {
            goodsList.push(res);
          }
        }
      });
      // 是否需要扫描电子监管码whetherRegulatory值0为否 ，1为是
      if (
        whetherRegulatory === 1
      ) {
        if (goodsList.length === 0) {
          this.$message.warning("请获取需要扫描电子监管码的商品！");
          return;
        }
        const obj = {
          allocationCode: allocationCode,
          productCode: productCode,
          goods: goodsList,
          orderCode: orderCode,
          mergeOrderCode: this.mergeOrderCode,
          largeCategoryCode: largeCategoryCode,
        };
        this.$refs.tracingCodeScanDialog.open(obj);
        this.hasDialog = true;
      }
    },
    // 设置展示商品主信息
    setReviewDetails(obj) {

      const tableData = this.tableData;
      this.formData.src = obj.productPic[0] ? obj.productPic[0] : require("@/assets/images/product.png");

      Object.assign(this.formData, obj);
      // 商品图片
      this.formData.src = obj.productPic[0] ? obj.productPic[0] : require("@/assets/images/product.png");
      let realPickingNumber = 0;
      const batchNumberList = [];
      const produceTimeList = [];
      const validDateList = [];
      for (let i = 0; i < tableData.length; i++) {
        const d = tableData[i];
        if (d.productCode === obj.productCode) {
          this.inNum++;
        }
      }

      const batchNumberMap = {};
      const produceTimeMap = {};
      const validDateMap = {};

      // 计算批号数量
      for (let i = 0; i < tableData.length; i++) {
        const d = tableData[i];
        if (d.productCode === obj.productCode) {
          const batchNumber = d.batchNumber;
          if (batchNumberMap[batchNumber]) {
            batchNumberMap[batchNumber] =
              parseInt(batchNumberMap[batchNumber]) + parseInt(d.reviewNumber);
          } else {
            batchNumberMap[batchNumber] = d.reviewNumber;
            produceTimeMap[batchNumber] = d.produceTime;
            validDateMap[batchNumber] = d.validDate;
          }
          realPickingNumber += parseInt(d.reviewNumber);
        }
      }

      let ll = 0;
      let batchNumberMapLength = 0;
      for (const key in batchNumberMap) {
        // eslint-disable-line no-unused-vars
        batchNumberMapLength++;
      }

      for (const key in batchNumberMap) {
        const batchNumber = key;
        const reviewNumber = batchNumberMap[batchNumber];
        const produceTime = produceTimeMap[batchNumber];
        const validDate = validDateMap[batchNumber];

        if (ll === batchNumberMapLength - 1) {
          batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
          produceTimeList.push(produceTime);
          validDateList.push(validDate);
        } else {
          batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
          produceTimeList.push(produceTime);
          validDateList.push(validDate);
        }
        ll++;
      }
      // 批号转化
      const batchNum = batchNumberList.toString().replace('"', "");
      // 商品批号
      this.formData.batchNumber = batchNum;
      // 日期转化
      const produceTime1 = produceTimeList.toString();
      // 循环多个生产日期
      if (produceTimeList.length > 1) {
        this.formData.produceTime = produceTimeList
          .map((item) => {
            return XEUtils.toDateString(item, "yyyy-MM-dd");
          })
          .join(",")
          .replace("[]", "");
      } else {
        // 生产日期日期转换
        this.formData.produceTime = XEUtils.toDateString(
          produceTime1,
          "yyyy-MM-dd"
        );
      }
      const validDate1 = validDateList.toString();
      // 循环多个生产日期
      if (validDateList.length > 1) {
        this.formData.validDate = validDateList
          .map((item) => {
            return XEUtils.toDateString(item, "yyyy-MM-dd");
          })
          .join(",")
          .replace("[]", "");
      } else {
        // 有效期至日期转换
        this.formData.validDate = XEUtils.toDateString(
          validDate1,
          "yyyy-MM-dd"
        );
      }
      // 复核数量
      this.formData.reviewNumber = realPickingNumber;
    },

    // 双击选中行并定位 \ 选中相同商品(同编码)
    checkAndPosition(obj, needTTS) {
      const params = {
        productCode: obj ? obj.productCode : this.formRight.barCode,
        mergeOrderCode: this.mergeOrderCode,
      };


      // 商品复核请求
      review(params).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.setRightNumber(result);
          this.$message.success(msg);
          this.apilistPartsInReviewGoodsView(); // 刷新列表
          if (needTTS) {
            const tableRow = this.tableData;
            let num = 0;
            tableRow.forEach((i, v) => {
              if (params.productCode === i.productCode) {
                num += i.reviewNumber;
              }
            });
            this.dottsFunction(num + obj.packingUnit);
          }
        } else {
          this.$message.error(msg);
        }
      });
    },

    scrollTop(row, column) {
      this.$refs.reviewTable.scrollToRow(row, column);
    },
    // 查看任务
    searchBtn() {
      this.$refs.viewTasksDialog.open();
      this.hasDialog = true;
    },
    // 复核确认
    reviewBtn() {
      if (this.boxTableData.length === 0) {
        this.$message.error("请先输入耗材码后再进行操作");
        return;
      }
      this.apireviewConfirm();
    },

    // esc关闭弹窗后继续操作，快捷键不可操作
    closeDialog() {
      this.hasDialog = false;
    },

    // 查看任务弹窗关闭后运单号获取焦点
    closeViewTasksDialog() {
      this.hasDialog = false;
      this.$refs.erpOrderCode.focus();
    },

    // 查看异常提交弹窗关闭后运单号获取焦点
    closeExceptionSubmitDialog() {
      this.hasDialog = false;
      // this.clearPage()
      this.isSearchErp = false;
      this.$refs.erpOrderCode.focus();
      this.apilistPartsInReviewGoodsView();
    },

    //-------------------------------api-----------------------------
    genID() {
      const length = 10
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36)
    },
    //复核打包-获取复核的商品
    apilistPartsInReviewGoodsView() {
      // this.formRight.totalWeight = 0
      // this.formRight.totalVolume  = 0
      let reviewNum = 0
      listPartsInReviewGoodsView({
        orderCode: this.formRight.orderCode,
        allocationCode: this.formRight.allocationCode,
        mergeOrderCode: this.mergeOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          // console.log(result)
          const data = JSON.parse(JSON.stringify(result));
          data.forEach(item => {
            item.rowId = this.genID()
          })
          // data.forEach(item => {
          //   this.formRight.totalWeight += (Number(item.packingWeight) * Number( item.reviewNumber))
          //   this.formRight.totalVolume += (Number(item.packingVolume) * Number(item.reviewNumber))
          //   item.goodWeight = (Number(item.packingWeight))
          //   item.goodVolume = (Number(item.packingVolume))
          //   item.goodWeight = item.goodWeight ? item.goodWeight + 'kg' : ''
          //   item.goodVolume = item.goodVolume ? item.goodVolume + 'cm³' : ''
          // })
          // this.formRight.totalWeight = this.formRight.totalWeight ? Number(this.formRight.totalWeight).toFixed(3) + 'kg' : ''
          // this.formRight.totalVolume = this.formRight.totalVolume ? Math.round(Number(this.formRight.totalVolume)) + 'cm³' : ''
          this.tableData = JSON.parse(JSON.stringify(data));
          // console.log(this.tableData, 'this.tableData');

          this.tableData.forEach(item => {
            if (item.productPic == null) {
              item.productPic = []
            }
            item.erpOrderCode = this.formRight.erpOrderCode
            item.orderCode = item.mergeOrderCode
            item.reviewNumberPieces = item.reviewNumber / item.specification
            if (item.reviewStatus == 2) {
              reviewNum++
            }
          })
          // console.log(this.tableData.length, reviewNum, '12345');
          if (this.tableData.length === reviewNum && this.formRightArr[2].totalValue == this.formRightArr[2].checkedValue) {
            this.$refs.boxCode.select()
          } else {
            this.$refs.barCode.select();
          }
          // console.log(this.formRightArr, 'this.formRightArr');
        } else {
          this.$message.error(msg);
        }
      });
    },

    //获取拼箱列表
    apigetPartsInReviewGoodsView() {
      const params = {
        mergeOrderCode: this.mergeOrderCode
      }
      getMergeOrderList(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          this.boxTableData = result.filter(item => item.boxCode != '' && item.boxType != null)
        } else {
          this.$message.error(msg)
        }
      })
    },

    //删除拼箱
    deleteBox(row) {
      // const params = {
      //   mergeOrderCode: this.mergeOrderCode,
      //   consolidationCode: row.consolidationCode
      // }
      // deleteMergeOrder(params).then(res => {
      //   const {code, msg, result} = res
      //   if(code === 0){
      //     this.$message.success(msg)
      //     // this.apigetPartsInReviewGoodsView()
      //   }else{
      //     this.$message.error(msg)
      //   }
      // })
      this.boxTableData.splice(this.boxTableData.indexOf(row), 1)
      this.formRight.consolidationNumber = this.boxTableData.length
    },

    //销售单号搜索
    apigetPartsInReviewGoodsSalesInfo() {
      getPartsInReviewGoodsSalesInfo({
        erpOrderCode: this.formRight.erpOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          if (result.wholeFlag === true) {
            this.$alert('该复核任务有整件任务', '提示', {
              confirmButtonText: '确定',
              type: 'warning',
            }).then(() => {
              this.formRight.inreviewProductNumber = result.inreviewProductNumber;
              this.formRight.consolidationCode = result.consolidationCode;
              this.formRight.orderCode = result.orderCode;
              this.formRight.allocationCode = result.allocationCode;
              this.formRight.supervisoryCodeAcquisition =
                result.supervisoryCodeAcquisition;
              this.formRight.orderNumberCancelled = result.orderNumberCancelled;
              this.formRight.carrierName = result.carrierName;
              this.isMajorClients = result.isMajorClients;
              this.mergeOrderCode = result.mergeOrderCode;
              this.setRightNumber(result);
              this.tablePage = { pageNum: 1, pageSize: 15, total: 0 };
              this.apivideoStart(result.mergeOrderCode, result.isVideoEnabled);
              this.apilistPartsInReviewGoodsView();
              this.isSearchErp = true;
            })
          } else {
            this.formRight.inreviewProductNumber = result.inreviewProductNumber;
            this.formRight.consolidationCode = result.consolidationCode;
            this.formRight.orderCode = result.orderCode;
            this.formRight.allocationCode = result.allocationCode;
            this.formRight.supervisoryCodeAcquisition =
              result.supervisoryCodeAcquisition;
            this.formRight.orderNumberCancelled = result.orderNumberCancelled;
            this.formRight.carrierName = result.carrierName;
            this.isMajorClients = result.isMajorClients;
            this.mergeOrderCode = result.mergeOrderCode;
            this.setRightNumber(result);
            this.tablePage = { pageNum: 1, pageSize: 15, total: 0 };
            // this.$refs.boxCode.focus();
            // if (!result.consolidationCode) {
            //   this.addBoxCodeOpen = false;
            //   this.$nextTick(() => {
            //     this.$refs.barCode.focus();
            //   });
            // } else {
            //   this.addBoxCodeOpen = true;
            //   this.$nextTick(() => {
            //     this.$refs.boxCode.focus();
            //   });
            // }
            // this.$refs.barCode.focus();
            this.apivideoStart(result.mergeOrderCode, result.isVideoEnabled);
            this.apilistPartsInReviewGoodsView();
            this.isSearchErp = true;
            // this.apigetPartsInReviewGoodsView()
          }
        } else {
          this.$refs.erpOrderCode.focus();
          this.clearPage();
          this.$message.error(msg);
        }
      });
    },
    //开启摄像头
    apivideoStart(mergeOrderCode, isVideoEnabled) {
      const options = {
        method: "POST",
        url: "http://127.0.0.1:9095/video/start?bizCode=" + mergeOrderCode,
      };
      Axios(options).then((res) => {
        const { code, msg, result } = res.data;
        if (code === 0) {
          this.videoId = result;
          clearInterval(this.intervalId);
          this.apivideoPing();
        } else {
          this.$message.error(msg || "摄像头开启失败");
          if (isVideoEnabled === "1") {
            setTimeout(() => {
              this.apivideoStart(mergeOrderCode, isVideoEnabled);
            }, 1000);
          }
        }
      }).catch((err) => {
        if (isVideoEnabled !== "1") {
          return
        }
        this.$alert('请打开摄像头后点击重试?', '提示', {
          confirmButtonText: '重试',
          type: 'warning',
          callback: () => {
            this.apivideoStart(mergeOrderCode, isVideoEnabled);
          }
        })
      });

    },

    //维持摄像头心跳
    apivideoPing() {
      this.intervalId = setInterval(() => {
        const options = {
          method: "POST",
          url: "http://127.0.0.1:9095/video/ping?videoId=" + this.videoId,
        };

        Axios(options).then((res) => {
          const { code, msg, result } = res.data;
          if (code !== 0) {
            this.$message.error(msg || "无法连接视频监控客户端");
          }
        });
      }, 5000);
    },
    apisaveBoxCode() {
      const params = {
        orderCode: this.formRight.orderCode,
        mergeOrderCode: this.mergeOrderCode,
        boxCode: this.formRight.boxCode,
      };
      modifyConsolidation(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          this.$message.success("保存成功");
          this.formRight.consolidationCode = undefined;
          this.$refs.barCode.focus();
          // this.addBoxCodeOpen = false;
          this.apilistPartsInReviewGoodsView();
          // this.apigetPartsInReviewGoodsView()
        } else {
          this.formRight.boxCode = "";
          this.$refs.boxCode.focus();
          this.$message.error(msg);
        }
      });
    },
    apiCheckBoxCode() {
      // if(this.boxTableData.length > 0){
      //   if(this.boxTableData.findIndex(item => item.boxCode == this.formRight.boxCode) > -1){
      //     this.$message.warning('该耗材码已扫描')
      //     return
      //   }
      // }
      const params = {
        boxCode: this.formRight.boxCode,
      }
      let reviewNum = 0
      checkParts(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          this.$message.success(msg)
          this.boxTableData.push(result)
          this.formRight.boxCode = ''
          this.apilistPartsInReviewGoodsView();
          this.formRight.consolidationNumber = this.boxTableData.length
        } else {
          this.formRight.boxCode = ''
          this.$message.error(msg)
        }
      })
      setTimeout(() => {
        this.tableData.forEach(item => {
          if (item.reviewStatus == 2) {
            reviewNum++
          }
        })
        if (this.tableData.length == reviewNum && this.formRightArr[2].totalValue == this.formRightArr[2].checkedValue) {
          // console.log(114514);

          this.$refs.boxCode.focus()
        } else {
          this.$refs.barCode.select();
        }
      }, 1000)
    },
    apireview(allocationCode, productCode, cancel, orderCode) {
      review({
        allocationCode: allocationCode,
        productCode: productCode,
        orderCode: orderCode,
        cancel: cancel,
        mergeOrderCode: this.mergeOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.apilistPartsInReviewGoodsView();
          this.setRightNumber(result);
          this.$message.success(msg);
        } else {
          this.formRight.barCode = "";
          this.$refs.barCode.focus();
          this.$message.error(msg);
        }
      });
    },
    apireviewConfirm() {
      this.mainLoading = true;
      reviewConfirm({
        orderCode: this.formRight.orderCode,
        mergeOrderCode: this.mergeOrderCode,
        checkCancelOrder: this.checkCancelOrder === 1 ? undefined : 0,
        boxCodeList: this.boxTableData.map(item => item.boxCode)
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg, result } = res;
        this.checkCancelOrder = 1;
        if (code === 0) {
          if (result.isPrint == 1) {
            printNew(result.printContent)
          }
          this.clearPage();
          this.$message.success(msg);
          this.isSearchErp = false;
          this.$nextTick(() => {
            this.$refs.erpOrderCode.focus();
          });
        } else if (code === 2000) {
          this.$confirm(msg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.checkCancelOrder = 0;
              this.apireviewConfirm();
            })
            .catch(() => {
              this.checkCancelOrder = 0;
            });
        } else if (code === 2001) {
          this.clearPage();
          this.$refs.erpOrderCode.focus();
          this.$message.error(msg);
        } else if (code === 2002) {
          this.$message({
            message: msg,
            type: "warning",
            onClose() {
              window.parent.location.href = "/logout";
            },
          });
          // this.$confirm(msg, "提示", {
          //   confirmButtonText: "确定",
          //   cancelButtonText: "取消",
          //   type: "warning",
          // })
          //   .then(() => {
          //     window.parent.location.href = "/logout";
          //   })
          //   .catch(() => {
          //     console.log("取消操作");
          //   });
        } else {
          this.$message.error(msg);
        }
      });
    },

    setRightNumber(result) {
      this.formRightArr = [];
      this.formRight.productNumberAll = result.productNumberAll;
      this.formRight.productNumber = result.productNumber;
      this.formRightArr.push({ name: '商品总数量', totalValue: result.productNumberAll, checkedValue: result.productNumber })
      this.formRight.inreviewProductNumberAll = result.inreviewProductNumberAll;
      this.formRight.inreviewProductNumber = result.inreviewProductNumber;
      this.formRightArr.push({ name: '品种数', totalValue: result.inreviewProductNumberAll, checkedValue: result.inreviewProductNumber })
      this.formRight.orderNumberAll = result.orderNumberAll;
      this.formRight.orderNumber = result.orderNumber;
      this.formRightArr.push({ name: '随货同行数', totalValue: result.orderNumberAll, checkedValue: result.orderNumber })
      this.formRight.consolidationNumber = this.boxTableData.length;
      this.formRight.carrierName = result.carrierName;
      this.formRight.orderNumberCancelled = result.orderNumberCancelled;
    },

    setRowReviewStates() {
      if (this.tableData.length > 0) {
        this.formRight.barCode = "";
        this.$refs.barCode.focus();
      } else {
        this.$refs.erpOrderCode.focus();
        this.isSearchErp = false
      }
    },

    showExceptionSubmissionDialog() {
      // 获取复选框选中的行数据
      const rowData = this.$refs.reviewTable.getCheckboxRecords();
      const selectRow = [];
      if (rowData.length > 0) {
        rowData.forEach((item) => {
          // if (item.reviewStatus === 2) {
          //   this.$message.warning("已复核商品无法进行异常提交!");
          //   return false;
          // }
          // if (item.exceptionCause !== null && item.exceptionCause !== "") {
          //   this.$message.warning("已经提交过异常的商品无法再次提交异常!");
          //   return false;
          // }
          selectRow.push(item);
        });
        // 打开异常提交弹窗
        this.$refs.exceptionSubmitDialog.open(selectRow, this.mergeOrderCode);
        this.hasDialog = true;
      } else {
        this.$refs.exceptionSubmitDialog.open(selectRow, this.mergeOrderCode);
      }
    },

    //----------------------------tool-----------------------
    //语音播报
    dottsFunction(ttsMsg) {
      doTTS(ttsMsg);
    },
  },
};
</script>
<style>
.review-table .cell-white {
  background-color: white;
}

.review-table .cell-green {
  background-color: lightgreen;
}

.review-table .cell-red {
  background-color: red;
}

.review-table .cell-blue {
  background-color: lightblue;
}

/* .review-table .cell-white :hover {
  background-color: rgb(159, 233, 240);
}
.review-table .cell-green :hover{
  background-color: rgb(157, 239, 157);
}
.review-table .cell-red :hover{
  background-color: rgb(240, 150, 150);
} */
</style>
<style lang="scss" scoped>
.warning-class {
  font-size: 36px;
  font-weight: 500;
  color: red;
}

.el-card-left {
  float: left;
  width: 330px;
}

.el-card {
  padding: 6px 15px;

  .clearfix[data-v-8d34e40c] {
    display: inline-block;
    float: none;
  }

  .header-right {
    line-height: 30px;
    font-size: 14px;
    float: right;

    label {
      display: inline-block;
      width: 60px;
    }
  }
}

.el-main {
  border: 1px solid #e8eaec;
  background-color: #fff;
}

.form-right {
  background-color: #fff;
  padding: 20px;
  float: right;
  border: 1px solid #e8eaec;

  .el-form-item {
    .el-input {
      width: 270px !important;
    }
  }
}

.large-size {
  font-size: 36px;
  color: blue;
  font-weight: bold;
  display: inline-block;
  width: 740px;
  white-space: nowrap;
  /*强制span不换行*/
  overflow: hidden;
  /*超出宽度部分隐藏*/
}

.large-black-size {
  font-size: 36px;
  font-weight: bold;
  line-height: 36px;
}

.medium-size {
  font-weight: bold;
  font-size: 28px;
  line-height: 28px;
}

.el-form-item,
.el-form-item--small.el-form-item {
  height: 45px;
}

.el-form-item__label {
  font-weight: unset;
  font-size: 16px;
  width: 92px;
}

.el-image {
  width: 300px;
  height: 300px;
}

.head-title {
  text-align: center;
  border-top: 1px solid #e8eaec;
  border-bottom: 1px solid #e8eaec;
  height: 60px;
  line-height: 60px;
  font-size: 20px;
}

.item-label {
  line-height: 30px;
  font-weight: unset;
  font-size: 19px;
}

.item-number {
  font-size: 30px;
}

.row {
  white-space: nowrap;
}
</style>

<style scoped>
.ul-list-container {
  margin-top: 20px;
  width: 100%;
}

.ul-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.ul-list-item {
  font-size: 14px;
  width: 25%;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: 1px solid lightgray;
}

.row-carrierName {
  width: 100%;
  height: 60px;
  border-top: 1px solid lightgray;
  border-bottom: 1px solid lightgray;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  font-weight: bold;
  color: blue;
}

.gift-class {
  color: #2db7f5;
  font-size: 24px;
}
</style>
