import request from "@/utils/request";



/**
 * 运费规则-承运商下拉查询
 * @param {Object} data
 */
 export function logisticsProvidersQueryAll(params) {
  return request({
    url: 'salesOrder2B/salesController/getSalesOrderSelectData',
    method: 'post',
    data: params
  })
}

/**
 *   单据打印获取数据列表
 * @param {object}
 */
export function getOrderPrintListNew(data) {
  return request({
    url: "/outstock/web/outstock2c/wave/print/listPickingTask",
    method: "post",
    data: data,
  });
}

/**
 * 单据打印获取批捡单详细信息
 * @param {object}
 */
export function getOrderPrintDetailListNew(data) {
  return request({
    url: "/outstock/web/outstock2c/wave/print/listSalesOrder",
    method: "post",
    data: data,
  });
}

/**
 *  单据打印打印接口
 *  @param {object}
 */
export function printingOrdersNew(data) {
  return request({
    url: "stockoutNew/printLog2BNew/printingOrdersNew",
    method: "post",
    data: data,
  });
}

/**
 *  分播单打印
 *  
 *  @param {object}
 *  */
export function printPickingTaskNew(data) {
  return request({
    url: "/outstock/web/outstock2c/wave/print/getTotalSubBroadcastPrint",
    method: "post",
    data: data,
  });
}

/**
 * 面单打印
 * @param {object}
 * */
export function printLogisticsNew(data) {
  return request({
    url: "/outstock/web/outstock2c/wave/print/listExpressSheetPrint",
    method: "post",
    data: data,
  });
}
