import request from "@/utils/request"

// 购进退出单复核列表
export function buyExitRreviewList(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/queryPerformDocument',
      method: 'post',
      data
    }
  )
}
// 购进退出复核明细列表
export function buyExitRreviewDetailList(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/queryPerformDocumentDetail',
      method: 'post',
      data
    }
  )
}
// 购进退出复核提交
export function buyExitRreviewSubmit(data) {
  return request(
    {
      url: '/outstock/web/purchase/refundOrder/saveOrderApproved',
      method: 'post',
      data
    }
  )
}
// 打印的接口
export function buyExitRreviewPrinter(pickUpOrder) {
  return request(
    {
      url: `/outstock/web/purchase/refundOrder/refundReviewOrderDetailPrint?pickUpOrder=${pickUpOrder}&printType=3`,
      method: 'get',
    }
  )
}
// 复核校验用户权限
 export function loginReview(data){
  return request(
    {
      url: '/outstock/web/common/verifyPermission',
      method: 'post',
      data
    }
  )
 }