import Layout from "@/layout";

/** 销售出库管理 **/
export default {
  path: "/outstock",
  component: Layout,
  alwaysShow: true,
  redirect: "/outstock/sellorder",
  meta: {
    title: "销售出库管理",
    icon: "head<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    code: "menu:wms:outstock",
  },
  children: [
    {
      path: "/supply/outstock/assignmanage", //调度管理
      component: () => import("@/views/components/shellView/index.vue"),
      name: "outstockassignmanage",
      alwaysShow: true,
      meta: {
        title: "调度管理",
        icon: "headtiaoduguan<PERSON>",
        code: "menu:wms:outstockassignmanage",
      },
      children: [
        {
          path: "/outstock/assign/sellorder", //正常销售订单
          component: () =>
            import("@/views/outStockManage/assign/sellOrder/index"),
          name: "outstockassignsellorder",
          meta: {
            title: "正常销售订单",
            // icon: "headdingdanxinxi",
            code: "menu:wms:normalsellorder",
          },
        },
        {
          path: "/outstock/assign/specialorder", //快速出库订单
          component: () =>
            import("@/views/outStockManage/assign/specialOrder/index"),
          name: "outstockassignspecialorder",
          meta: {
            title: "快速出库订单",
            // icon: "headkuaisuchukudan",
            code: "menu:wms:quiksellorder",
          },
        },
        {
          path: "/outstock/assign/onbill", //拣货任务管理
          component: () => import("@/views/outStockManage/assign/onbill/index"),
          name: "outstockassignonbill",
          meta: {
            title: "拣货任务管理",
            // icon: "headjianhuoguanli",
            code: "menu:wms:checktaskmanage",
          },
        },
        {
          path: "/outstock/assign/stockoutCancelOrder",
          component: () =>
            import(
              "@/views/outStockManage/assign/stockoutCancelOrder/index.vue"
            ),
          name: "outstockassignstockoutCancelOrder",
          meta: {
            title: "取消订单",
            // icon: "headshibai-baocuo",
            code: "menu:wms:cancelOrder",
          },
        },
        {
          path: "/outstock/assign/designatedLotNumberSend", // 指定批号下发
          component: () =>
            import(
              "@/views/outStockManage/assign/designatedLotNumberSend/index.vue"
            ),
          name: "designatedLotNumberSend",
          meta: {
            title: "指定批号下发",
            // icon: "headpihao",
            code: "menu:wms:designatedLotNumberSend",
          },
        },
        {
          path: "/outstock/assign/stockoutInterfaceLog", //出库接口日志
          component: () => import("@/views/outStockManage/assign/stockoutInterfaceLog/index"),
          name: "stockoutInterfaceLog",
          meta: {
            title: "出库接口日志",
            // icon: "headshibai-baocuo",
            code: "menu:wms:stockoutInterfaceLog",
          }
        }
      ],
    },
    {
      path: "/supply/outstock/printmanage", //打印管理
      component: () => import("@/views/components/shellView/index.vue"),
      name: "outstockprintmanage",
      alwaysShow: true,
      meta: {
        title: "打印管理",
        icon: "headdayinguanliqi",
        code: "menu:wms:printmanage",
      },
      children: [
        {
          path: "/outstock/print/scatteredOrder", //零货订单打印
          component: () =>
            import("@/views/outStockManage/print/scatteredOrder"),
          name: "outstockprintscatteredOrder",
          meta: {
            title: "零货订单打印",
            // icon: "headweidingdandayin",
            code: "menu:wms:smallgoodsorderprint",
          },
        },
        {
          path: "/outstock/print/wholeOrder", //纯整订单打印
          component: () => import("@/views/outStockManage/print/wholeOrder"),
          name: "outstockprintwholeOrder",
          meta: {
            title: "纯整订单打印",
            // icon: "heada-dayindayinji",
            code: "menu:wms:wholeOrder",
          },
        },
        {
          path: "/outstock/print/stockoutTagPrint",
          component: () =>
            import("@/views/outStockManage/print/stockoutTagPrint/index.vue"),
          name: "stockoutTagPrint",
          meta: {
            title: "出库标签打印",
            // icon: "headbiaoqiandayin",
            code: "menu:wms:stockoutTagPrint",
          },
        },
        {
          path: "/outstock/print/wholeBillPrint",
          component: () => import('@/views/outStockManage/print/wholeBillPrint/index.vue'),
          name: 'wholeBillPrint',
          meta: {
            title: '整件面单打印',
            code: 'menu:wms:wholeBillPrint'
          }
        },
      ],
    },
    {
      path: "/supply/outstock/wholePick", //拣货作业
      component: () => import("@/views/components/shellView/index.vue"),
      name: "outstockwholePick",
      alwaysShow: true,
      meta: {
        title: "拣货作业",
        icon: "headjianhuozuoye",
        code: "menu:wms:pickwork",
      },
      children: [
        {
          path: "/outstock/wholePick/gettingTask", //整件任务索取
          component: () =>
            import("@/views/outStockManage/wholePick/gettingTask"),
          name: "gettingTask",
          meta: {
            title: "整件任务索取",
            // icon: "headtongbujiluchaxun",
            code: "menu:wms:gettingTask",
          },
        },
        {
          path: "/outstock/wholePick/searchTask", //整件任务查询
          component: () =>
            import("@/views/outStockManage/wholePick/searchTask"),
          name: "searchTask",
          meta: {
            title: "整件任务查询",
            // icon: "headrenwuchaxun",
            code: "menu:wms:searchTask",
          },
        },
        {
          path: "/stockout/pickByPage",
          component: () =>
            import("@/views/outStockManage/pickbypage/index.vue"),
          name: "pickbypage",
          meta: {
            title: "纸单拣货",
            // icon: "headjianhuohuowei",
            code: "menu:wms:pickByPage",
          },
        },
        {
          path: "/stockout/pickbyelectronictag",
          component: () =>
            import("@/views/outStockManage/pickbyelectronictag/index.vue"),
          name: "pickbyelectronictag",
          meta: {
            title: "电子标签拣货",
            // icon: "headjianhuohuowei",
            code: "test",
          },
        },
        // {
        //   path: '/stockout/pickByForeground',
        //   component: () =>
        //     import("@/views/outStockManage/pickByForeground/index.vue"),
        //   name: 'pickByForeground',
        //   meta: {
        //     title: '前置仓拣货',
        //     // icon: "headjianhuohuowei",
        //     code: "menu:wms:pickByForeground",
        //   }
        // }
        // {
        //   path: "/outstock/partsPick/gettingTask",   //零货任务索取
        //   component: () => import("@/views/outStockManage/partsPick/gettingTask"),
        //   name: "outstockpartsPickgettingTask",
        //   meta: { title: "零货任务索取", icon: "el-icon-monitor", code:'test' },
        // },
        // {
        //   path: "/outstock/partsPick/searchTask",   //零货任务查询
        //   component: () => import("@/views/outStockManage/partsPick/searchTask"),
        //   name: "outstockpartsPicksearchTask",
        //   meta: { title: "零货任务查询", icon: "el-icon-monitor", code:'test' },
        // },
      ],
    },
    {
      path: "/outstock/reviewPackage", //拼团复核打包
      component: () => import("@/views/outStockManage/reviewPackage"),
      name: "outstockreviewPackage",
      meta: {
        title: "拼团复核打包",
        icon: "headdabao",
        code: "menu:wms:reviewPackage",
      },
    },
    {
      path: "/outstock/weight/weightManager", //称重
      component: () => import("@/views/outStockManage/weight/weightManager"),
      name: "outStockManageWeightManager",
      meta: {
        title: "称重",
        icon: "headchengzhong",
        code: "menu:wms:weightManager",
      },
    },
    // {
    //   path: '/outstock/unusualProblem', //出库异常处理
    //   component: () => import('@/views/components/shellView/index.vue'),
    //   name: 'outstockunusualProblem',
    //   alwaysShow: true,
    //   meta: {
    //     title: '出库异常处理',
    //     icon: 'el-icon-monitor',
    //     code: 'menu:wms:unusualProblem',
    //   },
    //   children: [
    //     {
    //       path: "/outstock/unusualProblem/pickup",   //拣货异常处理
    //       component: () => import("@/views/outStockManage/unusualProblem/pickup"),
    //       name: "unusualProblempickup",
    //       meta: { title: "拣货异常处理", icon: "el-icon-monitor", code:'menu:wms:unusualProblem:pickup' },
    //     },
    //     {
    //       path: "/outstock/unusualProblem/review",   //复核还货回库
    //       component: () => import("@/views/outStockManage/unusualProblem/review"),
    //       name: "unusualProblempickup",
    //       meta: { title: "复核还货回库", icon: "el-icon-monitor", code:'menu:wms:unusualProblem:review' },
    //     },
    //   ]
    // },
    {
      path: "/outstock/stockoutException",
      component: () => import("@/views/components/shellView/index.vue"),
      name: "stockoutException",
      alwaysShow: true,
      meta: {
        title: "出库异常处理",
        icon: "headyichangchuli",
        code: "menu:wms:unusualProblem",
      },
      children: [
        {
          path: "/outstock/stockoutException/stockoutExceptionHandle",
          component: () =>
            import(
              "@/views/outStockManage/stockoutException/stockoutExceptionHandle/index.vue"
            ),
          name: "stockoutExceptionHandle",
          meta: {
            title: "异常问题处理",
            // icon: "headwentichuli",
            code: "menu:wms:stockoutExceptionHandle",
          },
        },
        {
          path: "/outstock/stockoutException/storageBack",
          component: () =>
            import(
              "@/views/outStockManage/stockoutException/storageBack/index.vue"
            ),
          name: "storageBack",
          meta: {
            title: "复核回库",
            // icon: "headhuikuguanli",
            code: "menu:wms:storageBack",
          },
        },
      ],
    },
    { //PO导入
      path: "/outstock/poImport",
      component: () => import("@/views/outStockManage/poImport/index.vue"),
      name: "poImport",
      meta: {
        title: "PO导入",
        icon: "headxiazaidaoru",
        code: "menu:wms:poImport",
      }
    },
    {
      path: "/outstock/newReviewPackage", //新版拼团复核打包
      component: () => import("@/views/outStockManage/newReviewPackage"),
      name: "outstocknewReviewPackage",
      meta: {
        title: "新版拼团复核打包",
        icon: "headdabao",
        code: "menu:wms:newReviewPackage",
      },
    },
  ],
};
