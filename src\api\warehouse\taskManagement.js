import request from '@/utils/request' 
//终端上架任务管理-翻页查询
export function getTaskList(data) {
    return request({
        url: '/instock/purchase/storageOrderTerminal/findList',
        method: 'post',
        data: data
    })
}

//终端上架任务管理-根据容器号解绑上架任务
export function unbindTask(data) {
    return request({
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/instock/purchase/storageOrderTerminal/unBindStorageOrderByContainerCode',
        method: 'post',
        params: data
    })
}

//终端上架任务导出
export function exportTask(data) {
    return request({
        // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        url: '/export/submitExportTask',
        method: 'post',
        // params: data
        data: data
    })
}