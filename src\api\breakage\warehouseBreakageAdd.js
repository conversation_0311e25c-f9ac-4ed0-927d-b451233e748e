import request from "@/utils/request";

//获取字典枚举值
export function getDictCode(query) {
  return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
  });
}

//获取报损单号
export function getBreakageCode(data) {
  return request({
    url: "/warehouse/breakage/applyNo",
    method: "post",
    data: data,
  });
}

//新增行获取商品信息
export function getGoodsInfo(data) {
  return request({
    url: "/warehouse/stock/findGoodsAllocationProducts",
    method: "post",
    data: data,
  });
}

//获取报损单列表
export function getBreakageList(data) {
  return request({
    url: "/warehouse/breakage/pageQuery",
    method: "post",
    data: data,
  });
}

//不合格品报损单详情
export function getBreakageDetail(data) {
  return request({
    url: "/warehouse/warehouseCheck/overflowDetailQuery",
    method: "post",
    data: data,
  });
}

//新增报损单
export function addBreakage(data) {
  return request({
    url: "/warehouse/breakage/save",
    method: "post",
    data: data,
  });
}

//获取是否追溯参数
export function getTraceability(data) {
  return request({
    url: "/basicdata/dictParam/getByCode",
    method: "post",
    data: data,
  });
}

//添加盘损商品
export function addLossGoods(data) {
  return request({
    url: "/warehouse/breakage/addGoods",
    method: "post",
    data: data,
  });
}

//盘盈商品列表查询
export function getOverflowList(data) {
  return request({
    url: "/warehouse/warehouseCheck/selectSurplusGoods",
    method: "post",
    data: data,
  });
}

//修改损益原因
export function updateOverflowReason(data) {
  return request({
    url: "/warehouse/warehouseCheck/updateBillReason",
    method: "post",
    data: data,
  });
}

/**
 * 查询追溯码
 * @param {data} 请求体
 */
export function getQueryOrderDrugregulatorycode(data) {
  return request({
    url: "/warehouse/breakageMonitor/queryOrderDrugregulatorycode",
    method: "post",
    data: data,
  });
}

/**
 * 删除追溯码扫码记录
 * @param {data} 请求体
 */
export function getDeleteOrderDrugregulatorycode(data) {
  return request({
    url: "/warehouse/breakageMonitor/deleteCode",
    method: "post",
    data: data,
  });
}

/**
 * 追溯码扫码录入
 * @param {data} 请求体
 */
export function getAddMonitor(data) {
  return request({
    url: "/warehouse/breakageMonitor/breakageMonitor/addMonitor",
    method: "post",
    data: data,
  });
}

/**
 * 解锁
 * @param {data} 请求体
 */
export function getCheckPermissionBystaffNum(data) {
  return request({
    url: "checkPermissionBystaffNum",
    method: "post",
    data: data,
  });
}

/**
 * 查询单据列表
 * @param {data} 请求体
 */
export function getList(data) {
  return request({
    url: "/warehouse/breakageMonitor/getList",
    method: "post",
    data: data,
  });
}

/**
 * 查询单据详情列表
 * @param {data} 请求体
 */
export function getMonitorDeatils(data) {
  return request({
    Headers: { "Content-Type": "application/x-www-form-urlencoded" },
    url: "/warehouse/breakageMonitor/getMonitorDetails",
    method: "post",
    params: data,
  });
}

//删除盘点数据
export function deleteOverflow(data) {
  return request({
    url: "/warehouse/warehouseCheck/deleteBillWait",
    method: "post",
    data: data,
  });
}

//提取报损单明细
export function getBreakageDetailList(data) {
  return request({
  url: "/warehouse/breakage/extract",
  method: "post",
  data: data,
  });
}
