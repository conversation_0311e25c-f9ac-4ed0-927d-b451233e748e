import request from '@/utils/request'

/**
 * 入库作业->药检补录->查询
 * @param {data} 请求体
 */
export function findNoUploadList(data) {
  return request({
    url: '/instock/drugTestReport/findNoUploadList',
    method: 'post',
    data: data
  })
}

/**
 * 入库作业->药检补录->修改保存
 * @param {data} 请求体
 */
 export function drugTestReportUpdate(data) {
    return request({
      url: '/instock/drugTestReport/update',
      method: 'post',
      data: data
    })
  }

/**
 * 入库作业->药检补录->预览
 * @param {data} 请求体
 */
 export function drugTestReportPreview(data) {
  return request({
    url: '/instock/drugTestReport/preview',
    method: 'post',
    data: data
  })
}


/**
 * 入库作业->药检补录->导出
 * @param {data} 请求体
 */
 export function exportNoUploadList(data) {
  return request({
    url: '/purchase/drugTestReport/exportNoUploadList',
    method: 'post',
    data: data
  })
}
