import request from "@/utils/request";

/**
 * 业务类型列表查询
 * @param {Object} data
 */
export function getOwnerChannelList(params) {
  return request({
    url: "/basicdata/ownerChannel/getOwnerChannelVOList",
    method: "get",
    params,
  });
}

/**
 * 商品检验报告-查询
 * @param {Object} data
 */
export function drugTestReportSelectList(params) {
  return request({
    url: "/basicdata/master/drugTestReport/selectList",
    method: "post",
    data: params,
  });
}
/**
 * 商品检验报告-图片-保存
 * @param {Object} data
 */
export function drugTestReportSave(params) {
  return request({
    url: "/basicdata/master/drugTestReport/save",
    method: "post",
    data: params,
  });
}

/**
 * 商品检验报告-图片-更新
 * @param {Object} data
 */
export function drugTestReportUpdate(params) {
  return request({
    url: "/basicdata/master/drugTestReport/update",
    method: "post",
    data: params,
  });
}

/**
 * 商品检验报告-图片-上传
 * @param {Object} data
 */
export function drugTestReportUpload(params) {
  return request({
    url: "/basicdata/master/drugTestReport/uploadImg",
    method: "get",
    params: params,
  });
}

/**
 * 商品检验报告-图片-预览
 * @param {Object} data
 */
export function drugTestReportPreview(params) {
  return request({
    url: "/basicdata/master/drugTestReport/preview",
    method: "post",
    data: params,
  });
}

/**
 * 商品检验报告-检验报告下载
 * @param {Object} data
 */
export function drugTestReportDownload(params) {
  return request({
    url: "/basicdata/master/drugTestReport/download",
    method: "post",
    data: params,
    responseType: "blob",
  });
}

/**
 * 商品检验报告-检验报告批量下载
 * @param {Object} data
 */
export function drugTestReportMultiDownload(params) {
  return request({
    url: "/basicdata/master/drugTestReport/multiDownload",
    method: "post",
    data: params,
  });
}

/**
 * 商品检验报告-检验报告导出
 * @param {Object} data
 */
export function drugTestReportMultiExportExcel(params) {
  return request({
    url: "/basicdata/master/drugTestReport/exportExcel",
    method: "post",
    data: params,
  });
}

/**
 * 商品检验报告-获取参数字典
 * @param {Object} data
 */
export function drugTestReportGetDict(params) {
  return request({
    url: "/basicdata/master/drugTestReport/getDict",
    method: "get",
    data: params,
  });
}

/************基础资料 start************* */
/**
 * 数据字典-容器类型
 */
export function getByDictTypeList(param) {
  return request({
    url: "/basicdata/dictBases/getByDictTypeList",
    method: "post",
    data: param,
  });
}

/************基础资料 start************* */
/**
 * 分页查询商品批号
 */
export function batchNumPage(param) {
  return request({
    url: "/basicdata/master/batchNum/page",
    method: "post",
    data: param,
  });
}
/**
 * 基础资料-图片预览
 */
export function exceptionShowImgs(param) {
  return request({
      url: '/basicdata/master/drugTestReport/preview',
      method: 'post',
      data:param
  })
}
/**
 * 基础资料-打印
 */
export function drugTestReportPrint(param) {
  return request({
      url: 'basicdata/master/drugTestReport/print',
      method: 'post',
      data:param
  })
}

/**
 * 基础资料-效期商品管理
 */
export function queryValidateGoods(){
  return request({
      url: '/basicdata/product/validity/config/list',
      method: 'post',
      data:{}
  })
}

/**
 * 基础资料-商品效期配置保存
 */
export function saveValidateGoods(params){
  return request({
      url: '/basicdata/product/validity/config/save',
      method: 'post',
      data:params
  })
}