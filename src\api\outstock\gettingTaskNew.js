import request from "@/utils/request";

//整件任务索取新查询任务
export function findReplenishList(data) {
  return request({
    url: "warehouse/replenish/findReplenishList",
    method: "post",
    data: data,
  });
}

/**
 * 索取补货
 */
export function askForDownTask(params) {
  return request({
    url: "warehouse/replenish/askForDownTask",
    method: "post",
    data: params,
  });
}

/**
 * 重打标签
 */
export function rePrintTag(params) {
  return request({
    url: "warehouse/replenish/rePrintTag",
    method: "post",
    data: params,
  });
}

/**
 * 重打提炼单
 */
export function rePrintWholeList(params) {
  return request({
    url: "warehouse/replenish/batchDown",
    method: "post",
    data: params,
  });
}

/**
 * 重打提炼单
 */
export function getStatistics() {
  return request({
    url: "warehouse/replenish/statistics",
    method: "post",
  });
}

/**
 * 重打提炼单
 */
export function getRePrintWholeList(data) {
  return request({
    url: "warehouse/replenish/rePrintWholeList",
    method: "post",
    data: data,
  });
}
