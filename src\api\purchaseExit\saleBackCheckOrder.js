import request from "@/utils/request";

/**
 * 销售退回-销售退回验收单查询
 */
export function queryCheckListPage(param) {
  return request({
    url: "/instock/salesreturn/check/queryCheckListPage",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-销售退回验收单明细查询
 */
export function findCheckByCheckCode(param) {
  return request({
    url: "/instock/salesreturn/check/findCheckByCheckCode",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-销售退回验收单明细表单查询
 */
export function findCheckDetails(param) {
  return request({
    url: "/instock/salesreturn/check/findCheckDetails",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-确认验收
 */
export function confirmCheck(param) {
  return request({
    url: "/instock/salesreturn/check/confirmCheck",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-验收员2校验
 */
export function checkPermissionBystaffNum(param) {
  return request({
    url: "/instock/common/checkPermissionBystaffNum",
    method: "post",
    data: param,
  });
}
