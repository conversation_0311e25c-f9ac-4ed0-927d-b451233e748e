import request from '@/utils/request'
// import { formData } from '@/utils'
// ----------------------新建收货单接口---------------------------
/**
 *  获取待确认入库订单
 * @param {object} 查询实体
 */
export function getConfirmOrderList(data) {
  return request({
    url: '/consumables/subscribeStorageOrder/findWaitConfirmOrderList',
    method: 'post',
    data: data
  })
}
/**
 *  获取待确认入库订单明细
 * @param {object} 查询实体
 */
export function getConfirmOrderDetailList(data) {
  return request({
    url: '/consumables/subscribeStorageOrder/findWaitConfirmOrderDetailList',
    method: 'post',
    data: data
  })
}
/**
 *  获取入库确认
 * @param {object} 查询实体
 *  传id参数
 * 明细id\备注remark
 * {
  "detailDtoList": [{
    "id": 20000,
    "storageRemark": "备注",
    "subscribeOrderDetailId": 500000,
    "subscribeStorageOrderId": 10000
  }],
  "id": 10000
}
 */
export function confirming(data) {
  return request({
    url: '/consumables/subscribeStorageOrder/confirming',
    method: 'post',
    data: data
  })
}
/**
 *  获取驳回
 * @param {object} 查询实体
 *{
    "id": 20000,
    "storageRemark": "备注",
    "subscribeOrderDetailId": 500000,
    "subscribeStorageOrderId": 10000
  }
 */
export function turnDown(data) {
  return request({
    url: '/consumables/subscribeStorageOrder/turnDown',
    method: 'post',
    data: data
  })
}

