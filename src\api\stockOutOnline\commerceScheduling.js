import request from '@/utils/request'

/**
 *
 * 下拉查询字典
 *
 */
export function getDropDownQueryOptions() {
  return request({
    url: '/outstock/web/outstock2c/salesorder/getSalesOrderSelectData',
    method: 'get'
  })
}
/**
 *   电商调度-订单池列表
 * @param {object} 查询实体
 */
export function getOrderList(params) {
  return request({
    url: '/outstock/web/outstock2c/salesorder/list',
    method: 'post',
    data: params
  })
}
/**
 *   配送路线
 * @param {object} 查询实体
 */
export function getDeliveryRoute(params) {
  return request({
    url: 'master/customerBase/vagueDeliveryRouteInterface',
    method: 'post',
    data: params
  })
}
/**
 *   滚动播报
 *    TODO
 */
export function getRollingBroadcast() {
  return request({
    url: 'salesOrder2B/salesController/findUnAffirmJobNumber?taskType=1',
    method: 'post'
  })
}
/**
 *   统计页面
 *   TODO
 */
export function getStatisticsPage() {
  return request({
    url: 'salesOrder2B/salesController/findCount',
    method: 'post'
  })
}

/**
 *   订单池补货列表
 *   @param {object} 查询实体
 */
export function getOrderPoolReplenishmentList(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/listReplenishmentProduct',
    method: 'post',
    data: params
  })
}

/** ***
 * 出库单详情
 *
 */
export function getOutboundOrderDetails(params) {
  return request({
    url: '/outstock/web/outstock2c/salesorder/detail?orderCode='+params.orderCode,
    method: 'get',
    data: {}
  })
}

/** ***
 * 冲红原因
 *
 */
export function getredCause() {
  return request({
    url: '/outstock/web/outstock2c/salesorder/rushred/findRushRedReasons',
    method: 'get'
  })
}
/** ***
 * 冲红商品列表
 *
 */
export function getRedGoodsList(params) {
  return request({
    url: '/outstock/web/outstock2c/salesorder/rushred/findRushRedList',
    method: 'post',
    data: params
  })
}

/** ***
 * 冲红-保存
 *
 */
export function getSaveRedFlush(params) {
  return request({
    url: '/outstock/web/outstock2c/salesorder/rushred/saveRushRed',
    method: 'post',
    data: params
  })
}

/** ***
 * 创建波次
 *
 */
export function getCreateWave(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/arrange',
    method: 'post',
    data: params
  })
}

/** ***
 * 订单池补货
 *
 */
export function getOrdePpoolreplenishment(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/createReplenishmentTask',
    method: 'post',
    data: params
  })
}

/** ***
 * 查看波次下发列表
 *
 */
export function getWaveDistributionList(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/list',
    method: 'post',
    data: params
  })
}
/** ***
 * 查看波次下发明细列表
 *
 */
export function getWaveDistributionDetailList(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/findWaveDetailListToB',
    method: 'post',
    data: params
  })
}

/** ***
 * 批次补货列表
 *Delete
 */
export function getBatchReplenishmentList(params) {
  return request({
    url: 'replenishmentTaskToB/findReplenishmentOrderBatchList',
    method: 'post',
    data: params
  })
}
/** ***
 * 批次移库列表
 *Delete
 */
export function getBatchShiftLibraryList(params) {
  return request({
    url: 'replenishmentTaskToB/findReplenishmentOrderMovementList',
    method: 'post',
    data: params
  })
}
/** **
 * 批次移库
 *Delete
 */
export function getBatchTransfert(params) {
  return request({
    url: 'replenishmentTaskToB/saveReplenishmentMovement2B',
    method: 'post',
    data: params
  })
}
/** **
 * 批次补货
 *Delete
 */
export function getBatchReplenishment(params) {
  return request({
    url: 'replenishmentTaskToB/saveReplenishmentProductOfBatch2B',
    method: 'post',
    data: params
  })
}
/** ***
 * 移除波次单据明细
 *
 */
export function removeWaveDocumentDetails(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/removeOrder',
    method: 'post',
    data: params
  })
}
/** ***
 * 取消波次
 *
 */
export function cancelWaveSend(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/cancel',
    method: 'post',
    data: params
  })
}
/** ***
 * 试算波次下发 二次确认波次下发
 *
 */
export function trialListWaveSend(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/publish',
    method: 'post',
    data: params
  })
}

/** ***
 * 波次待处理列表
 *
 */
export function setWaveToBeProcessed(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/getPendingWaveList',
    method: 'post',
    data: params
  })
}
/** ***
 * 波次待处理订单统计列表
 *
 */
export function setWaveToBeProcessedOrderList(params) {
  return request({
    url: '/outstock/stockout/inreview2b/findWaveOrderStatisticsList',
    method: 'post',
    params: params
  })
}

/** ***
 * 波次统计列表
 *
 */
export function listOfWaveStatistics(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/waveProgressStatisticsToB',
    method: 'post',
    data: params
  })
}
/** ***
 * 波次统计明细
 *
 */
export function listOfWaveStatisticsDetail(params) {
  return request({
    url: '/outstock/web/outstock2c/wave/findWaveDetailListToB',
    method: 'post',
    data: params
  })
}
/** ***
 * 补货任务查看
 *  Delete
 */
export function replenishmentTaskView(params) {
  return request({
    url: 'replenishmentTaskToB/findReplenishmentTaskList',
    method: 'post',
    data: params
  })
}

/** ***
 * 提总和杂单接口
 * 1提总，2杂单
 * 
 */
 export function pickingTask2BControllerPack(params) {
  return request({
    url: '/outstock/web/outstock2c/packTask/pack',
    method: 'post',
    data: params
  })
}

