import request from '@/utils/request'
/**
 *  获取商品列表
 * @param {object} 查询实体
 */
export function getListByOrder(data) {
  return request({
    url: '/purchaseOrder/findList',
    method: 'post',
    data: data
  })
}

/**
 *  获取商品订单行列表
 * @param {object} 查询实体
 */
export function getInfoListByOrder(data) {
  return request({
    url: '/instock/receiveOrder/initExtractByOrder',
    method: 'post',
    data: data
  })
}

/**
 *  获取采购员列表
 * @param {object} 查询实体
 */
export function getPurchaseBuyerList(data) {
  return request({
    url: '/purchase/common/selectListSysPrivUser',
    method: 'get',
    params: data
  })
}

/**
 *  获取商品选择列表
 * @param {object} 查询实体
 */
export function getProductList(data) {
  return request({
    url: '/master/productBase/selectListProductBasePurchase',
    method: 'get',
    params: data
  })
}

// /**
//  *  通过商品名称查询商品订单行列表
//  * @param {object} 查询实体
//  */
// export function getProductListByProductName(data) {
//   return request({
//     url: '/purchaseOrderProduct/findListByOrder',
//     method: 'get',
//     params: data
//   })
// }

/**
 *  印模印章图片列表
 * @param {supplierCode } 供应商编号
 */
export function getEnclosureImage(supplierCode) {
  return request({
    url: '/instock/common/enclosureImage',
    method: 'post',
    data: {supplierCode}
  })
}

/**
 *  获取采购订单列表 --模糊搜索
 * @param { purchaseOrderCode } 匹配值
 */
export function getPurchaseList(purchaseOrderCode) {
  return request({
    url: '/purchaseOrder/getList4VagueCode',
    method: 'get',
    params: { purchaseOrderCode }
  })
}

/**
 *  获取业主、建筑物和商品类型
 * @param { purchaseOrderCode } 匹配值
 */
export function getOptions() {
  return request({
    url: '/purchase/receiveOrder/initExtractByOrder',
    method: 'get'
  })
}

/**
 *  提交数据
 * @param { receiveOrder } 数据对象
 */
export function submitData(receiveOrder) {
  return request({
    url: '/purchase/receiveOrder/save',
    method: 'post',
    data: receiveOrder
  })
}
