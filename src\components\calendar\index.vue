<template>
  <div class="calendar">
    <slot name="header">
      <div class="top">
        <div class="current">{{ time }}</div>
      </div>
    </slot>
    <div class="date">
      <div class="item">日</div>
      <div class="item">一</div>
      <div class="item">二</div>
      <div class="item">三</div>
      <div class="item">四</div>
      <div class="item">五</div>
      <div class="item">六</div>
      <template v-for="(item,index) in list">
        <div
          class="item"
          :class="getClass(item)"
          :key="item.type"
          @click="click(item)"
        >
          <slot
            :index="index"
            :item="item"
          >{{ item.next||item.prve?'':item.date }}</slot>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  name: "Calendar",
  props: {
    value: { type: Date, default: () => new Date() },
    range: { type: String, default: "2022-03-01" }, //月份
  },
  data() {
    return {
      list: [],
      time: "",
      currentSelectDate: "",
      currentDate: "",
      selectArray: [],
      zmArray: [],
      jjrArray: [],
      gzrArray: [],
      cjjArray: [],
    };
  },
  watch: {
    value(a) {
      if (a) this.time = a;
      this.btn(0);
    },
    range(newvalue) {
      if (newvalue) {
        this.getTime();
      }
    },
  },
  created() {
    this.getTime();
  },

  methods: {
    getClass(item) {
      return {
        active: this.selectArray.indexOf(item.type) !== -1,
        jjr:
          this.jjrArray.indexOf(item.type) !== -1 && !item.prve && !item.next,
        zm: this.zmArray.indexOf(item.type) !== -1 && !item.prve && !item.next,
        gzr:
          this.gzrArray.indexOf(item.type) !== -1 && !item.prve && !item.next,
        cjj: this.cjjArray.indexOf(item.type) !== -1 && !item.prve && !item.next,
        current: this.currentDate == item.type,
        grey: item.next || item.prve,
      };
    },
    setItems(items) {
      console.log(items.length);
      let itemsStr = items;
      this.gzrArray = [];
      this.zmArray = [];
      this.jjrArray = [];
      this.cjjArray = [];
      this.list.forEach((item, index) => {
        if (!item.next && !item.prve) {
          let type = itemsStr.substring(0, 1);
          itemsStr = itemsStr.substring(1, itemsStr.length);
          if (type === "1") {
            this.gzrArray.push(item.type);
          } else if (type === "2") {
            this.zmArray.push(item.type);
          } else if (type === "3") {
            this.jjrArray.push(item.type);
          }else{
            this.cjjArray.push(item.type);
          }
        }
      });
      console.log(items.length - itemsStr.length, "-------------");
      return itemsStr;
    },
    setzmArray() {
      this.cancelANYArray();
      this.zmArray = this.zmArray.concat(this.selectArray);
      this.selectArray = [];
    },
    setJJRArray() {
      this.cancelANYArray();
      this.jjrArray = this.jjrArray.concat(this.selectArray);
      this.selectArray = [];
    },
    setGZRArray() {
      this.cancelANYArray();
      this.gzrArray = this.gzrArray.concat(this.selectArray);
      this.selectArray = [];
    },
    setCJJArray() {
      this.cancelANYArray();
      this.cjjArray = this.cjjArray.concat(this.selectArray);
      this.selectArray = [];
    },
    cancelANYArray() {
      this.zmArray = this.zmArray.filter((item) => {
        return this.selectArray.indexOf(item) === -1;
      });
      this.jjrArray = this.jjrArray.filter((item) => {
        return this.selectArray.indexOf(item) === -1;
      });
      this.gzrArray = this.gzrArray.filter((item) => {
        return this.selectArray.indexOf(item) === -1;
      });
      this.cjjArray = this.cjjArray.filter((item) => {
        return this.selectArray.indexOf(item) === -1;
      });
    },
    clearSelect() {
      this.selectArray = [];
    },
    click(item) {
      let range = this.selectArray.indexOf(item.type);
      if (range !== -1) {
        // 取消选中
        this.selectArray.splice(range, 1);
        // 选中日期
      } else this.selectArray.push(item.type);
      this.$emit("click", { ...item, selectDate: item.type });
    },
    getTime() {
      if (this.range.length > 0) {
        this.rangeTime();
      } else {
        this.btn();
      }
    },

    rangeTime() {
      let currentYear = new Date(this.range).getFullYear();
      let currentMonth = new Date(this.range).getMonth() + 1;
      this.time = `${currentYear}-${
        currentMonth > 9 ? currentMonth : "0" + currentMonth
      }`;
      let date = `${currentYear}-${
        currentMonth > 9 ? currentMonth : "0" + currentMonth
      }-01`;
      this.init(date);
    },

    // 默认当前月
    btn(type = "currentMonth") {
      // 获取指定时间的年份和月份
      let currentYear = new Date(this.time).getFullYear();
      let currentMonth = new Date(this.time).getMonth() + 1;
      // 点击上个月
      if (type === "prevMonth") {
        // 指定时间的月份为1月，上一月为上一年的12月
        if (currentMonth === 1)
          (currentMonth = 13), (currentYear = currentYear - 1);
        currentMonth--;
      }
      // 当前月
      if (type === "currentMonth") {
        // 获取当前时间的年月日
        currentYear = new Date().getFullYear();
        currentMonth = new Date().getMonth() + 1;
        let currentDate = new Date().getDate();
        this.currentDate = `${currentYear}-${
          currentMonth > 9 ? currentMonth : "0" + currentMonth
        }-${currentDate > 9 ? currentDate : "0" + currentDate}`;
      }
      // 下个月
      if (type === "nextMonth") {
        // 指定时间的月份为12月，下一月为下一年的1月
        if (currentMonth === 12)
          (currentMonth = 0), (currentYear = currentYear + 1);
        currentMonth++;
      }
      // 当前日历展示的年月
      this.time = `${currentYear}-${
        currentMonth > 9 ? currentMonth : "0" + currentMonth
      }`;
      let date = `${currentYear}-${
        currentMonth > 9 ? currentMonth : "0" + currentMonth
      }-01`;
      this.init(date);
      this.$emit("change-month", { currentYear, currentMonth, type, date });
      this.$emit("update:current-date", this.currentDate);
    },
    // 生成日历日期，因每月开始和结束不是周日和周六，需要取上月月末和下月月初补满每周七天，所以生成的总天数共35-42天。
    init(time) {
      // 获得指定时间年月，月总天数
      let date = new Date(time);
      let currentYear = date.getFullYear(); // 年
      let currentMonth = date.getMonth() + 1; // 月
      let currentMonthDate = new Date(currentYear, currentMonth, 0).getDate(); // 当月总天数
      let list = new Array(currentMonthDate).fill().map((_, i) => ({
        year: currentYear,
        month: currentMonth,
        date: i + 1,
      }));

      // 获取上月，需要处理跨年
      let preMonth = currentMonth;
      if (preMonth === 1) (preMonth = 13), (currentYear = currentYear - 1);
      let beforeMonthDate = new Date(currentYear, preMonth - 1, 0).getDate(); // 上月总天数
      let beforeDate = new Date(
        `${currentYear}-${preMonth - 1}-${beforeMonthDate}`
      ).getDay();
      for (let i = 0; i <= beforeDate; i++)
        list.unshift({
          prve: true,
          year: currentYear,
          month: preMonth - 1,
          date: beforeMonthDate - i,
        });

      // 获取下月，需要处理跨年
      let nextMonth = currentMonth;
      if (nextMonth === 12) (nextMonth = 0), (currentYear = currentYear + 1);
      let afterDate = 7 - ((currentMonthDate + beforeDate) % 7); // 下月月头
      for (let i = 1; i < afterDate; i++)
        list.push({
          next: true,
          year: currentYear,
          month: nextMonth + 1,
          date: i,
        });
      this.list = list.map((e, i) => {
        let { year, month, date } = e;
        this.setWorkArray(e, i);
        return {
          ...e,
          week: Math.floor(i % 7),
          type: `${year}-${month > 9 ? month : "0" + month}-${
            date > 9 ? date : "0" + date
          }`,
        };
      });
    },
    setWorkArray(item, index) {
      let { year, month, date, next, prve } = item;
      let day = `${year}-${month > 9 ? month : "0" + month}-${
        date > 9 ? date : "0" + date
      }`;
      if (!next && !prve) {
        if (index % 7 === 0 || index % 7 === 6) {
          this.zmArray.push(day);
        } else {
          this.gzrArray.push(day);
        }
      }
    },
    getList() {
      let fuckStr = "";
      this.list.forEach((item) => {
        if (this.zmArray.indexOf(item.type) !== -1) {
          fuckStr += "2";
        } else if (this.jjrArray.indexOf(item.type) !== -1) {
          fuckStr += "3";
        }else if (this.cjjArray.indexOf(item.type) !== -1) {
          fuckStr += "4";
        } else {
          if (!item.next && !item.prve) {
            fuckStr += "1";
          }
        }
      });
      return fuckStr;
    },
  },
};
</script>
<style lang="scss" scoped>
.calendar {
  min-height: 300px;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    margin: 0 0 20px 0;
    padding: 0 10px;
  }
  .box {
    display: flex;
    justify-content: space-evenly;
    width: 160px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin: 5px;
    color: #666;
    .btn {
      padding: 3px 5px;
      cursor: pointer;
      transition: all 0.1s;
      &:nth-child(2) {
        border: 1px solid #eee;
        border-top: none;
        border-bottom: none;
      }
      &:hover {
        color: #333;
        background: #eee;
        transition: all 0.1s;
      }
    }
  }
  .item {
    flex: 0 0 12%;
    height: 35px;
    margin: 2px;
    min-width: 20px;
    text-align: center;
    color: #444;
    padding-top: 5px;
    box-sizing: border-box;
    border-radius: 5px;
    transition: all 0.2s;
    &.grey {
      color: #999;
    }
    &:hover {
      transition: all 0.2s;
      cursor: pointer;
      background: #5164e7a0;
      color: #fff;
    }
    &.current {
      transition: all 0.2s;
      background: #5164e7a0;
      color: #fff;
    }
    &.active {
      transition: all 0.2s;
      background: #5164e7;
      color: #fff;
    }
  }
  .zm {
    transition: all 0.2s;
    background: lightgray;
    color: #fff;
  }
  .jjr {
    transition: all 0.2s;
    background: orange;
    color: #fff;
  }
  .gzr {
    transition: all 0.2s;
    background: yellow;
    color: #333;
  }
  .cjj {
    transition: all 0.2s;
    background: #42b983;
    color: #333;
  }
  .date {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>