import request from '@/utils/request'
/**
 * 业主名称
 * @param {Object} data
 */
export function getDictListByDictType(params) {
    return request({
      url: 'dictBases/getDictListByDictType',
      method: 'get',
      params: params
    })
}
/**
 * 查询
 * @param {Object} params 
 * @returns 
 */
export function getHighShelvesGoodsAllocation(params) {
  return request({
    url: '/highShelves/movement/bill/queryHighShelvesGoodsAllocation',
    method: 'post',
    data: params
  })
}

/**
 * 生成移库任务单
 * @param {Object} params 
 * @returns 
 */
export function billSave(params) {
  return request({
    url: '/highShelves/movement/bill/save',
    method: 'post',
    data: params
  })
}