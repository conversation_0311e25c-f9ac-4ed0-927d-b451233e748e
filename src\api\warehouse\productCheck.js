import request from '@/utils/request'

//获取字典数据
export function getDictData(data) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: data
    })
}

//获取复查基础信息
export function getCheckBaseInfo(data) {
    return request({
        url: '/warehouse/warehouseLockProduct/lockProductAddView',
        method: 'post',
        data: data
    })
}

//分页查询商品复查审批单
export function getCheckList(data) {
    return request({
        url: '/warehouse/warehouseLockProduct/pageQuery',
        method: 'post',
        data: data
    })
}

//获取复查单详情商品明细
export function getCheckDetail(data) {
    return request({
        url: '/warehouse/warehouseLockProduct/pageQueryDetail',
        method: 'post',
        data: data
    })
}

//锁定商品复查审批
export function lockProductCheck(data) {
    return request({
        url: '/warehouse/warehouseLockProduct/productCheckOperate',
        method: 'post',
        data: data  
    })
}

//模糊查询商品编码
export function getGoodsCode(param) {
    return request({
        url: '/basicdata/master/productBase/queryByKeyword',
        method: 'post',
        data: param
    })
}