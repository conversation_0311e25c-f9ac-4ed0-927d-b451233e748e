import Layout from "@/layout"

export default {
    path: '/jobMonitor',
    component: Layout,
    redirect: '/jobMonitor/stockoutMonitor',
    alwaysShow: true,
    name: 'jobMonitor',
    meta: {
        title: '作业监控',
        icon: 'headjiankongcaozuo_N',
        code: 'menu:wms:jobMonitor'
    },
    children: [
        {
            path: '/jobMonitor/stockoutMonitor',
            component: () => import('@/views/jobMonitor/stockoutMonitor/index.vue'),
            name: 'stockoutMonitor',
            meta: {
                title: '出库监控',
                icon: 'headyuchuku',
                code: 'menu:wms:stockoutMonitor'
            }
        },
        {
            path: '/jobMonitor/purchaseMonitor',
            component: () => import('@/views/jobMonitor/purchaseMonitor/index.vue'),
            name: 'purchaseMonitor',
            meta: {
                title: '入库监控',
                icon: 'headrukuguanli',
                code: 'menu:wms:purchaseMonitor'
            }
        },
        {
            path: '/jobMonitor/jobProcessMonitor',
            component: () => import('@/views/jobMonitor/jobProcessMonitor/index.vue'),
            name: 'jobProcessMonitor',
            meta: {
                title: '作业进度监控',
                icon: 'headyuchuku',
                code: 'menu:wms:jobProcessMonitor'
            }
        },
        {
            path: '/jobMonitor/largeCustomersMonitor',
            component: () => import('@/views/jobMonitor/largeCustomersMonitor/index.vue'),
            name: 'largeCustomersMonitor',
            meta: {
                title: '大客户出库监控',
                icon: 'headyuchuku',
                code: 'menu:wms:largeCustomersMonitor'
            }
        },
        {
            path: '/jobMonitor/largeCustomeresDtail',
            component: () => import('@/views/jobMonitor/largeCustomeresDtail/index.vue'),
            name: 'largeCustomeresDtail',
            hidden: true,
            meta: {
                title: '大客户每日订单出库详情',
                icon: 'headyuchuku',
                code: 'menu:wms:largeCustomeresDtail'
            }
        },
    ]
}