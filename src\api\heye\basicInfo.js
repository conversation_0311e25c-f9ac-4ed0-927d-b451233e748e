import request from '@/utils/request'

/**
 * 列表 基础资料-商品资料
 * @param {Object} data
 */
export function apiGetReceiveList(params) {
    return request({
        url: '/equityCard/productInformation/pageProduct',
        method: 'post',
        data: params
    })
}

/**
 * 列表 基础资料-货位资料
 * @param {Object} data
 */
export function apiGetPositionReceiveList(params) {
  return request({
      url: '/equityCard/goodsPosition/pageQuery',
      method: 'post',
      data: params
  })
}

/**
 * 导入
 */
export function apigoodsPositionImport(params) {
  return request({
    url: '/equityCard/goodsPosition/import-excel',
    method: 'post',
    data: params
  })
}