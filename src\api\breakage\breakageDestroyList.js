import request from "@/utils/request";

//获取字典枚举值
export function getDictCode(query) {
    return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
    });
}

//不合格品销毁单列表查询
export function getBreakageDestroyList(data) {
    return request({
    url: "/warehouse/warehouseDestroy/selectList",
    method: "post",
    data: data,
    });
}

//不合格品销毁单详情查询
export function getBreakageDestroyDetail(data) {
    return request({
    url: "/warehouse/warehouseDestroy/selectDetails",
    method: "post",
    data: data,
    });
}