import request from "@/utils/request";

//出库标签补打—拼箱列表
export function getStocktagPrintList(data) {
    return request({
        url: "/outstock/web/outstock/inreview/listTag",
        method: "post",
        data
    });
}

//出库标签补打—整件标签列表
export function getStocktagPrintListWhole(data) {
    return request({
        url: "/outstock/web/outstock/whole/picking/listTag",
        method: "post",
        data
    });
}

//出库标签补打—拼箱打印
export function getStocktagPrint(data) {
    return request({
        url: "/outstock/web/outstock/inreview/listTagPrint",
        method: "post",
        data
    });
}

//出库标签补打—整件标签打印
export function getStocktagPrintWhole(data) {
    return request({
        url: "/outstock/web/outstock/whole/picking/getTagPrint",
        method: "post",
        data
    });
}