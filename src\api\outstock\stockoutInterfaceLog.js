import request from "@/utils/request";

// 出库订单下传列表查询
export function getStockoutInterfaceLogListDP(data) {
    return request({
        url: "/outstock/web/outstock/interfaceLog/findPageBase",
        method: "post",
        data
    });
}
//出库订单下传列表 -> 订单详情
export function getStockoutInterfaceLogDetail(data) {
    return request({
        url: "/outstock/web/outstock/interfaceLog/findProductBaseByOrder",
        method: "post",
        data
    });
}

//出库订单下传列表 -> 状态重置
export function resetStockoutInterfaceLogStatus(data) {
    return request({
        url: "/outstock/web/outstock/interfaceLog/updateWmflg",
        method: "post",
        data
    });
}

// 出库订单回传列表查询
export function getStockoutInterfaceLogListRT(data) {
    return request({
        url: "/outstock/web/outstock/interfaceLog/getSalesOrderPushErp",
        method: "post",
        data
    });
}