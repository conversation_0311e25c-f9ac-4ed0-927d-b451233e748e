import request from '@/utils/request'

/**
 * 字典
 * 入参格式 {items:["DSCYS","YZLX"]}
 * 
 * KHLX 客户类型 YZLX业主类型 GYSLX供应商类型 ABC分类、存储分类、存放属性  SPDL 商品大类  WPXL物品小类 WPDL物品大类  YZBM业主编码
 * DSCYS  承运商
 */
export function queryDictBase(param) {
    return request({
        url: '/basicdata/dictBases/getByDictTypeList',
        method: 'post',
        data: param
    })
}   
/**
 * 字段查询-单个   { dictType: "ABCFL" }
 */
export function queryDictSignle(param){
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: param
    })
}
/**
 * 库别
 */
export function queryStorageType() {
    return request({
        url: '/basicdata/dictBases/getAllStorageType',
        method: 'post',
        data: {}
    })
}
/**
 * 库房
 */
export function queryStorageRoomByType(param) {
    return request({
        url: '/basicdata/dictBases/getAllStorageRoomByType',
        method: 'post',
        data: param
    })
}   

//模糊查询商品编码
export function getGoodsCode(param) {
    return request({
        url: '/basicdata/master/productBase/queryByKeyword',
        method: 'post',
        data: param
    })
}

// 移库来源字典
export function getMoveTypeStateDict(){
    return request({
        url: 'warehouse/warehouseMovement/getMoveTypeStateDict',
        method: 'post',
        data: {}
    })
}