import request from '@/utils/request'
// import axios from 'axios'
/**
 *   获取安全库存查询数据
 * @param {object} 查询实体
 */
export function getSafeStock(data) {
  return request({
    url: '/consumablesSafeStock/getSafeStock',
    method: 'post',
    data: data
  })
}
/**
 *   获取商品类别下拉数据
 * @param {object} 查询实体
 */
export function TypeSelect(categoryType) {
  return request({
    url: 'consumables/basicdata/getTypeSelect',
    method: 'post',
    data: { categoryType }
  })
}
/**
 *   导出
 * @param {object} 查询实体
 */
export function getExportExcel(data) {
  return request({
    url: '/consumablesSafeStock/exportExcel',
    method: 'post',
    data
  })
}
