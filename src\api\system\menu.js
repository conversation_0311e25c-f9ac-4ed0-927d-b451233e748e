import request from '@/utils/request'

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: '/basicdata/system/menu/list',
    method: 'post',
    data: query
  })
}

// 查询菜单（精简)列表
export function listSimpleMenus() {
  return request({
    url: '/basicdata/system/menu/list-all-simple',
    method: 'post'
  })
}

// 查询菜单详细
export function getMenu(id) {
  return request({
    url: '/basicdata/system/menu/get',
    method: 'post',
    data: {id:id}
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/basicdata/system/menu/create',
    method: 'post',
    data: data
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/basicdata/system/menu/update',
    method: 'post',
    data: data
  })
}

// 删除菜单
export function delMenu(id) {
  return request({
    url: '/basicdata/system/menu/delete',
    method: 'post',
    data:{id:id}
  })
}

/**
 * 商品资料-枚举接口
 */
export function queryGoodsinfoDictByType(param) {
  return request({
      url: '/basicdata/dictBases/getByDictTypeList',
      method: 'post',
      data: param
  })
}