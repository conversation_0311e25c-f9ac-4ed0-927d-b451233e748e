import request from "@/utils/request";

//【异常问题处理】查询异常任务列表
export function getExceptionTaskList(data) {
  return request({
    url: "/outstock/web/outstock/exception/task/queryExceptionTaskList",
    method: "post",
    data,
  });
}

//【异常问题处理】多货处理
export function handleMoreGoods(data) {
    return request({
    // Headers: {'content-type': 'application/x-www-form-urlencoded'},
    url: "/outstock/web/outstock/exception/task/updateStatus",
    method: "post",
    params: data,
  });
}

//【异常问题处理】异常处理查询库存
export function getExceptionStock(data) {
  return request({
    url: "/outstock/web/outstock/exception/task/getExceptionHandleUseStorage",
    method: "get",
    params: data,
  });
}

//【异常问题处理】异常处理补货
export function handleExceptionStock(data) {
    return request({
    url: "/outstock/web/outstock/exception/task/exceptionHandlingReplenishment",
    method: "post",
    data,
  });
}

//【异常问题处理】处理冲红
export function handleRed(data) {
    return request({
    url: "/outstock/web/outstock/exception/task/confirmRushRed",
    method: "post",
    data,
  });
}

//【异常问题处理】打印
export function exceptionPrint(data){
    return request({
        url: "/outstock/web/outstock/exception/task/exceptionPrint",
        method: "get",
        params: data,
    });
}

//【异常问题处理】导出
export function exceptionExport(data){
    return request({
        url: "/export/submitExportTask",
        method: "post",
        data: data,
    });
}
