import request from '@/utils/request'

/**
 *  耗材库存查询列表
 * @param {object} 查询实体
 */
export function getStockList(params) {
  return request({
    url: '/consumables/consumablesStock/stockConsumablesRecord',
    method: 'post',
    data: params
  })
}
/**
 *  耗材库存列表导出
 * @param {object} 查询实体
 */
export function exportStockConsumablesExcel(params) {
  return request({
    url: 'consumablesStock/exportStockConsumablesExcel',
    method: 'post',
    data: params
  })
}

/**
 *  库存账页查询列表
 * @param {object} 查询实体
 */
export function getStockPagerRecordList(params) {
  return request({
    url: '/consumables/consumablesStock/stockPaperRecord',
    method: 'post',
    data: params
  })
}

/**
 *  库存账页列表导出
 * @param {object} 查询实体
 */
export function exportStockPaperExcel(params) {
  return request({
    url: 'consumablesStock/exportStockPaperExcel',
    method: 'post',
    data: params
  })
}
/**
 *  入库记录查询列表
 * @param {object} 查询实体
 */
export function getStorageRecordList(params) {
  return request({
    url: '/consumables/subscribeStorageOrder/getInStorageRecordList',
    method: 'post',
    data: params
  })
}

/**
 * 导出入库记录
 * @param params
 * @returns {AxiosPromise}
 */
export function exportStorageRecord(params) {
  return request({
    url: 'consumables/subscribeStorageOrder/getInStorageRecordList/export',
    method: 'post',
    data: params
  })
}

/**
 * 获取入库,出库 结果  的枚举
 * @param params
 * @returns {AxiosPromise}
 */
export function getStorageResultEnum(params) {
  return request({
    url: '/consumables/common/getEnums',
    method: 'post',
    data: params
  })
}

/**
 * 获取复核记录
 * @param params
 * @returns {AxiosPromise}
 */
export function getReviewRecord(params) {
  return request({
    url: '/consumables/stockoutInternalQuery/stockoutInternalQueryData',
    method: 'post',
    data: params
  })
}

/**
 * 复核记录-处理异常
 * @param void
 * @returns {AxiosPromise}
 */
export function handleSubstractExcepion() {
  return request({
    url: 'stockoutInternal/stockoutInternalSubstractExcepion',
    method: 'post'
  })
}

/**
 * 申领记录
 * @param params
 * @returns {AxiosPromise}
 */
export function getApplyRecord(params) {
  return request({
    url: '/consumables/stockout/stockoutDetail',
    method: 'post',
    data: params
  })
}

/**
 *  调整记录查询列表
 * @param {object} 查询实体
 */
export function getAdjustmentRecordList(params) {
  return request({
    url: '/consumables/consumablesStock/adjustRecord',
    method: 'post',
    data: params
  })
}

/**
 *  调整记录查询列表导出
 * @param {object} 查询实体
 */
export function adjustmentRecordExport(params) {
  return request({
    url: 'consumablesStock/exportAdjustRecordExcel',
    method: 'post',
    data: params
  })
}

/**
 *  周转箱纸箱使用记录查询列表
 * @param {object} 查询实体
 */
export function getCartonUsageRecordList(params) {
  return request({
    url: '/consumables/stockoutInternalQuery/stockoutInternalBoxQueryData',
    method: 'post',
    data: params
  })
}

/**
 *  周转箱纸箱使用记录查询列表导出
 * @param {object} 查询实体
 */
export function cartonUsageRecordExport(params) {
  return request({
    url: 'stockoutInternalQuery/exportStockoutInternalBoxQueryData',
    method: 'post',
    data: params
  })
}

/**
 *  台账查询列表
 * @param {object} 查询实体
 */
export function getAccountsRecordList(params) {
  return request({
    url: '/consumables/standingbook/findStandingbookPageList',
    method: 'post',
    data: params
  })
}

/**
 *  台账列表导出
 * @param {object} 查询实体
 */
export function accountsQueryExport(params) {
  return request({
    url: 'consumables/standingbook/exportDataExcel',
    method: 'post',
    data: params
  })
}

/**
 *  每日台账查询列表
 * @param {object} 查询实体
 */
export function getDayAccountsRecordList(params) {
  return request({
    url: '/consumables/standingbook/queryPagerListDay',
    method: 'post',
    data: params
  })
}

/**
 *  每日台账列表导出
 * @param {object} 查询实体
 */
export function dayAccountsQueryExport(params) {
  return request({
    url: 'consumables/standingbook/exportDataExcelDay',
    method: 'post',
    data: params

  })
}

/**
 * 导出复核记录
 * @param params
 * @returns {AxiosPromise}
 */
export function exportReviewRecord(params) {
  return request({
    url: 'stockoutInternalQuery/exportStockoutInternalQueryData',
    method: 'post',
    data: params
  })
}
/**
 * 导出申领记录
 * @param params
 * @returns {AxiosPromise}
 */
export function exportApplyRecord(params) {
  return request({
    url: 'stockout/exportStockoutDetailExcel',
    method: 'post',
    data: params
  })
}
/**
 * 库内记录查询
 * @param params
 */
export function getWarehouseRecordList(params) {
  return request({
    url: '/consumables/warehouseBoxStorage/findWarehouseBoxPage',
    method: 'post',
    data: params
  })
}/**
 * 库内记录导出
 * @param params
 * @returns {AxiosPromise}
 */
export function exportWarehouseRecord(params) {
  return request({
    url: 'consumables/warehouseBoxStorage/export',
    method: 'post',
    data: params
  })
}
/**
 * 复核记录查询-业主查询
 * @param params
 * @returns {AxiosPromise}
 */
 export function findOwnerList(params) {
  return request({
    url: '/consumables/stockoutInternalQuery/stockoutInternalQueryData',
    method: 'post',
    data: params
  })
}
/**
 * 复核记录查询-订单类型
 * @param params
 * @returns {AxiosPromise}
 */
 export function findOrderTypeCtrl(params) {
  return request({
    url: '/consumables/stockoutInternalQuery/findOrderTypeCtrl',
    method: 'post',
    data: params
  })
}