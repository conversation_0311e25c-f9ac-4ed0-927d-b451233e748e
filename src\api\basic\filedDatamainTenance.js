import request from '@/utils/request';

/*************字段资料维护************ */
/**
 * 基础资料-字典档案-字段资料维护
 */
export function getDictViewList(param) {
  return request({
    url: '/basicdata/dictView/getDictViewList',
    method: 'post',
    data: param,
  });
}

/**
 * 根据id查询 参数字典
 */
export function getById(param) {
  return request({
    url: '/basicdata/dictView/getById',
    method: 'post',
    data: param,
  });
}

/**
 * 根据DictName获取拼音大写的filedType
 */
export function getFiledType(param) {
  return request({
    url: '/basicdata/dictView/getFiledType',
    method: 'post',
    data: param,
  });
}

/**
 * 修改/添加字典数据
 */
export function savaOrUpdate(param) {
  return request({
    url: '/basicdata/dictView/savaOrUpdate',
    method: 'post',
    data: param,
  });
}
