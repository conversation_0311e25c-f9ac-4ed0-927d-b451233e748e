import request from "@/utils/request";

/**
 * 查询业主集合
 * @param {*} dictType 业主编码（业主类型：YZBM）
 */
export function getDictCodeType(dictType) {
  return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: { dictType },
  });
}
/**
 *  商品名称模糊查询
 * @param {object} 查询实体
 */
export function findProductPullTips(data) {
  return request({
    url: "/basicdata/master/productBase/queryByKeyword",
    method: "post",
    data: data,
  });
}
/**
 *  销售退回收货单查询委托-列表查询
 * @param {object} 查询实体
 */
export function queryReceiveListPage(data) {
  return request({
    url: "/instock/salesreturn/receive/queryReceiveListPage",
    method: "post",
    data: data,
  });
}

/**
 *  销售退回收货单查询委托-导出
 * @param {object} 查询实体
 */
export function getExportReceive(data) {
  return request({
    url: "/salesreturnReceive/exportReceive",
    method: "post",
    data: data,
  });
}

/**
 * 获取页面信息
 * @param {object} 查询实体
 */
export function getPageInfo(data) {
  return request({
    url: '/instock/salesreturn/receive/findReceiveByReceiveCode',
    method: 'post',
    data: data
  })
}

/**
 * 获取运输工具字典
 * @param {receiveCode} 收货单号
 */
export function getTransportList() {
  return request({
    url: '/basicdata/dictBases/getByDictType',
    method: 'get',
    params: {dictType:"YSGJ"}
  })
}

/**
 * 获取运输方式字典
 * @param { receiveCode } 收货单
 */
export function getModeOfTransportDict() {
  return request({
    url: '/basicdata/dictBases/getByDictType',
    method: 'get',
    params: {dictType:"YSFS"}
  })
}

/**
 * 获取拒收原因字典
 */
export function getRefuseReason() {
  return request({
    url: '/basicdata/dictBases/getByDictType',
    method: 'get',
    params: {dictType:"JSYY"}
  })
}

/**
 * 获取商品明细信息
 * @param {receiveCode} 查询实体
 */
export function getProductDetailList(params) {
  return request({
    url: '/instock/salesreturn/receive/queryReceiveDetailPage',
    method: 'post',
    data: params
  })
}

/**
 * 获取容器编号列表
 * @param {receiveCode} 查询实体
 */
export function getContainerList(data) {
  return request({
    url: '/instock/common/pageContainer',
    method: 'post',
    data: data
  })
}

/**
 * 确认收货
 * @param {data}
 */
export function confirmReceipt(data) {
  return request({
    url: '/instock/salesreturn/receive/confirmReceive',
    method: 'post',
    data: data
  })
}

