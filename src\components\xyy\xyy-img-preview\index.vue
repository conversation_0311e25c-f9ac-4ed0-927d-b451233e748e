<template>
  <div class="img-preview">
    <el-dialog
      ref="dialogTableVisible" 
      title="图片预览" 
      width="648px"
      :visible.sync="dialogVisible"
      >
      <div class="img-wrapper" title="点击查看大图">
        <i class="el-icon-arrow-left" @click="prev" />
        <i class="el-icon-arrow-right" @click="next" />
        <el-image
          style="width: 100%; height: 100%"
          :src="url"
          :preview-src-list="[url]"
        >
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </div>
      <div class="img-count">{{ index+1 }}/{{ urlList.length }}</div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'XyyImgPreview',
  // props: {
  // urlList: {
  //   type: Array,
  //   default: () => []
  // }
  // },
  data() {
    return {
      url: '',
      urlList: [],
      index: 0,
      dialogVisible: false
    }
  },
  created() {
    // this.url = this.urlList[0]
  },
  methods: {
    close() {
      this.$refs.dialogTableVisible.close()
    },
    open(urlList) {
      this.url = urlList[0]
      this.urlList = urlList
      this.index = 0
      this.dialogVisible = true
    },
    prev() {
      this.index--
      if (this.index < 0) {
        this.index = this.urlList.length - 1
      }
      this.url = this.urlList[this.index]
    },
    next() {
      this.index++
      if (this.index > this.urlList.length - 1) {
        this.index = 0
      }
      this.url = this.urlList[this.index]
    }
  }
}
</script>
<style lang="scss" scoped>
.img-wrapper{
    position: relative;
    height: 400px;
    font-size: 36px;
    .el-icon-arrow-left{
        position: absolute;
        left: 0;
        top: 200px;
        z-index: 1;
        cursor: pointer;
    }
     .el-icon-arrow-right{
        position: absolute;
        right: 0;
        top: 200px;
        z-index: 1;
        cursor: pointer;
    }
    .image-slot{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
}
.img-count{
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }
</style>
