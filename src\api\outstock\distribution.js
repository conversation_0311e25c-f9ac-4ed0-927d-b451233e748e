import request from '@/utils/request'

/**
 *   验证工号
 * @param {object} 
 */
 export function verificationJobNumber(data) {
    return request({
      url: 'wholeGoodsAllocation/WholeGoodsAllocationController/verificationJobNumber',
      method: 'post',
      params: data
    })
  }

/**
 *   分播位分配接口
 * @param {object} 
 */
 export function distributionOrder(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/distributionOrder',
    method: 'post',
    data: data
  })
}

/**
 *   分播订单取消拦截
 * @param {object} 
 */
 export function distributeIntercept(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/distributeIntercept',
    method: 'post',
    data: data
  })
}

/**
 *   商品分播
 * @param {object} 
 */
 export function distributionProduct(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/distributionProduct',
    method: 'post',
    data: data
  })
}

/**
 *   分播订单详情
 * @param {object} 
 */
 export function distributionOrderDetail(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/distributionOrderDetail',
    method: 'get',
    params: data
  })
}

/**
 *   分播完成
 * @param {object} 
 */
 export function distributionFinish(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/distributionFinish',
    method: 'post',
    data: data
  })
}

/**
 *   查询待分播任务数
 * @param {object} 
 */
 export function needDistributionTaskNumberCount(data) {
  return request({
    url: 'stockout/pc/GroupOrderDistributionControll/needDistributionTaskNumberCount',
    method: 'post',
    data: data
  })
}

/**
 *   查询待分播任务数
 * @param {object} 
 */
 export function getConsolidation(data) {
  return request({
    url: 'outstock/web/outstock/checkPicking/getCheckPackingLcLListByMergeOrderCode',
    method: 'post',
    data: data
  })
}

/**
 * 复核打包-删除拼箱
 * @param {object} 
 */
 export function deleteConsolidation(data) {
  return request({
    url: 'outstock/web/outstock/checkPicking/deleteConsolidation',
    method: 'post',
    data: data
  })
}

/**
 * 复核打包-新增/修改
 * @param {object} 
 */
 export function modifyConsolidation(data) {
  return request({
    url: 'outstock/web/outstock/checkPicking/modifyConsolidation',
    method: 'post',
    data: data
  })
}

  /**
   * 复核打包-提交异常
   * @param {object}
   */
  export function submitException(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/saveInReviewAbnormal',
      method: 'post',
      data: data
    })
}
