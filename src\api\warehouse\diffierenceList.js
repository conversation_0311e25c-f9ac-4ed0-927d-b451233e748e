import request from '@/utils/request'

/**
 * 获取驳回列表
 * @param {data} 请求体
 */
export function getQueryList(data) {
  return request({
    url: 'instock/purchase/purchaseDifferenceOrder/queryList',
    method: 'post',
    data: data
  })
}
/**
 * 导出
 * @param {data} 请求体
 */
export function getExport(data) {
  return request({
    url: 'instock/purchase/purchaseDifferenceOrder/queryList',
    method: 'post',
    data: data
  })
}

/**
 * 差异单号模糊搜索
 * @param {data} 请求体
 */
export function getQueryDifferenceOrderCode(data) {
  return request({
    url: '/instock/purchase/purchaseDifferenceOrder/queryDifferenceCode',
    method: 'post',
    data: data
  })
}
/**
 * 收货单号模糊搜索
 * @param {data} 请求体
 */
export function getList4VagueCode(data) {
  return request({
    url: '/instock/receiveOrder/getList4VagueCode',
    method: 'post',
    data: data
  })
}

