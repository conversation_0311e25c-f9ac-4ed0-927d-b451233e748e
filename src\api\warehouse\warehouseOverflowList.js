import request from "@/utils/request";

//获取字典字段
export function getDictCode(query) {
  return request({
    url: "/basicdata/dictBases/getByDictType",
    method: "get",
    params: query,
  });
}

//损益单列表
export function getOverflowList(data) {
  return request({
    url: "/warehouse/warehouseCheck/overflowQuery",
    method: "post",
    data: data,
  });
}

//损溢单明细查询
export function getOverflowDetail(data) {
  return request({
    url: "/warehouse/warehouseCheck/overflowDetailQuery",
    method: "post",
    data: data,
  });
}

