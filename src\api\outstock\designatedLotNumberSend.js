import request from '@/utils/request'

// 指定批号——出库单列表
export function designatedLotNumberLists(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/listSalesOrder',
    method: 'post',
    data
  })
}
// 指定批号——出库单明细列表
export function getDetailDesignatedNumber(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/listSalesOrderDetail',
    method: 'post',
    data
  })
}
// 指定批号——出库单明细指定批号列表
export function outstockDesignatedNumber(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/listSpecify',
    method: 'post',
    data
  })
}
// 指定批号——还原订单
export function resetSelectOrder(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/restore',
    method: 'post',
    data
  })
}
// 指定批号——保存指定批号
export function confirmDesignatedNumber(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/saveSpecify',
    method: 'post',
    data
  })
}

//指定批号——下发
export function designatedLotNumberSend(data) {
  return request({
    url: '/outstock/web/outstock/wave/specify/publish',
    method: 'post',
    data
  })
}
