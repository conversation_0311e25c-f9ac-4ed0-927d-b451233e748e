import request from '@/utils/request'
import { formData } from '@/utils/index'
// ----------------------新建收货单接口---------------------------
/**
 *  获取验收执行页面信息
 * @param {object} 查询实体
 */
export function getCheckOrder(params) {
  return request({
    url: 'purchase/checkOrder/selectPurchaseCheckOrderDetailNoPage',
    method: 'get',
    params: params
  })
}
/**
 *  获取当前登录用户信息
 * @param {object} 查询实体
 */
export function getCurrentUser(data) {
  return request({
    url: 'purchase/common/getCurrentUserForPurchase',
    method: 'post',
    data: data
  })
}
/**
 *  批号模糊搜索
 * @param {object} 查询实体
 */
export function getBatchCode(data) {
  return request({
    url: 'purchase/common/queryProductBatchCode',
    method: 'get',
    params: data
  })
}
/**
 *  不合格事项模糊搜索
 * @param {object} 查询实体
 */
export function getDictListByNameAndType(params) {
  return request({
    url: '/dictBases/getDictListByNameAndType',
    method: 'get',
    params: params
  })
}
/**
 *  不合格事项模糊搜索
 * @param {object} 查询实体
 */
export function getReason(params) {
  return request({
    url: 'dictBases/getDictListByNameAndType',
    method: 'get',
    params: params
  })
}
/**
 *  上架区域信息
 * @param {object} 查询实体
 */
export function getShelfArea(data) {
  return request({
    url: 'purchase/common/getStorageTypeArea',
    method: 'post',
    data: formData(data)
  })
}
/**
 *  获取容器列表
 * @param {object} 查询实体
 */
export function getContainerList(data) {
  return request({
    url: '/instock/common/pageContainer',
    method: 'post',
    data: data
  })
}
/**
 *  获取提交请求
 * @param {object} 查询实体
 */
export function submitCheck(data) {
  return request({
    url: 'purchase/checkOrder/checkResult',
    method: 'post',
    data: data
  })
}
/**
 *  获取驳回请求
 * @param {object} 查询实体
 */
export function getTurnDown(data) {
  return request({
    url: 'purchase/checkOrder/turnDown',
    method: 'post',
    data: data
  })
}
/**
 *  获取商品校验
 * @param {object} 查询实体
 */
export function productValidityBatchCheck(data) {
  return request({
    url: 'master/productValidityConfig/productValidityBatchCheck',
    method: 'post',
    data: data
  })
}
/**
 *  获取取消请求
 * @param {object} 查询实体
 */
export function cancel(data) {
  return request({
    url: 'purchase/checkOrder/bindOrNotPurchaseCheckOrderCancel',
    method: 'post',
    data: formData(data)
  })
}
/**
 * 登录
 * @param {object} 查询参数，包含username、password
 */
export function checkLogin(data) {
  return request({
    url: 'user/checkUserResource',
    method: 'post',
    data: formData(data)
  })
}

