import request from '@/utils/request'

/**
 * 获取页面信息
 * @param {object} 查询实体
 */
export function getPageInfo(data) {
  return request({
    url: 'salesreturnReceive/findJsReceiveByReceiveCode',
    method: 'post',
    data: data
  })
}

/**
 * 获取运输工具字典
 * @param {receiveCode} 收货单号
 */
export function getTransportList() {
  return request({
    url: 'salesreturnCommon/getMeansOfTransport',
    method: 'post'
  })
}

/**
 * 获取运输方式字典
 * @param { receiveCode } 收货单
 */
export function getModeOfTransportDict() {
  return request({
    url: 'salesreturnCommon/getModeOfTransport',
    method: 'post'
  })
}

/**
 * 获取拒收原因字典
 */
export function getRefuseReason() {
  return request({
    url: 'salesreturnCommon/getRefuseReason',
    method: 'post'
  })
}

/**
 * 获取商品明细信息
 * @param {receiveCode} 查询实体
 */
export function getProductDetailList(params) {
  return request({
    url: 'salesreturnReceive/findJsReceiveDetailByCode',
    method: 'post',
    data: params
  })
}

/**
 * 获取容器编号列表
 * @param {receiveCode} 查询实体
 */
export function getContainerList(params) {
  return request({
    url: 'sales/getContainerListByPage',
    method: 'get',
    params: params
  })
}

/**
 * 确认收货
 * @param {data}
 */
export function confirmReceipt(data) {
  return request({
    url: 'salesreturnReceive/updateReceiveDetailList',
    method: 'post',
    data: data
  })
}
