<template>
  <div class="app-container">
    <xyy-panel title="查询条件">
      <!-- 按钮组 end-->
      <btn-group slot="tools" :btn-list="btnList" />
      <el-form ref="form" :model="formData" label-width="90px" class="searchform">
        <el-row :gutter="20">
          <!-- <el-col :span="6" :md="6">
            <el-form-item label="近效期天数" prop="effDayEnd">
              <el-input v-model="formData.effDayEnd"></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
            <el-form-item label="商品编号" prop="productCode">
              <!-- <el-input v-model="formData.productCode"></el-input> -->
              <el-input v-model="formData.productName" disabled>
                <el-button slot="append" icon="el-icon-delete" @click="deleteProductName" />
                <el-button slot="append" icon="el-icon-search" @click="openProductName" />
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库别">
              <el-select v-model="formData.storageTypeCode" clearable filterable placeholder="请选择"
                @change="storageTypeChange" @clear="storageTypeClear">
                <el-option v-for="item in storageTypeCode" :key="item.dictCode" :value="item.dictCode"
                  :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库房">
              <el-select v-model="formData.storageRoomCode" clearable filterable placeholder="请选择"
                @focus="storageRoomFocus" @clear="storageRoomClear" @change="storageRoomChange">
                <el-option v-for="item in storageRoomCode" :key="item.dictCode" :value="item.dictCode"
                  :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库区">
              <el-select v-model="formData.storageAreaCode" clearable filterable placeholder="请选择"
                @focus="storageAreaFocus">
                <el-option v-for="item in storageAreaCode" :key="item.index" :value="item.storageAreaCode"
                  :label="item.storageAreaCode" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" :md="6">
            <el-form-item label="货位编码" prop="goodsAllocation">
              <el-input v-model="formData.goodsAllocation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :md="6">
            <el-form-item label="批号" prop="batchNumber">
              <el-input v-model="formData.batchNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="效期类型" prop="effectiveType">
              <el-select v-model="formData.effectiveType" clearable>
                <el-option v-for="item in effectiveType" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="业主名称" prop="ownerCode">
              <el-select v-model="formData.ownerCode" clearable>
                <el-option v-for="item in ownerCode" :key="item.dictCode" :value="item.dictCode"
                  :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <!-- <el-col :lg="6" :md="6">
            <el-form-item label="商品大类" prop="largeCategoryCode">
              <el-select v-model="formData.largeCategoryCode" clearable>
                <el-option
                  v-for="item in largeCategoryCode"
                  :key="item.dictCode"
                  :value="item.dictCode"
                  :label="item.dictName"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel title="商品近效期预警列表">
      <!--筛选列组件-->
      <div slot="tools" style="float:right;">
        <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
      </div>
      <div style="display: flex">
        <span>异常颜色示意：</span>
        <div style="margin-right: 11px; background-color: #e36262">过效期</div>
        <div style="margin-right: 11px; background-color: #fabcbc">近效期：0-6个月</div>
        <div style="background-color: #FFC300">预警效期：6-9个月</div>
      </div>
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" :data="tableData" highlight-current-row height="auto"
          :row-style="rowClassName" :seq-config="{
            startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,
          }" resizable @resizable-change="resizableChange" :columns="columns" :key="tableKey">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in columns">
            <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
              min-width="156px" max-width="220px">
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
      <div class="pager">
        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]" @page-change="handlePageChange" />
      </div>
    </xyy-panel>
    <productModal ref="productModal" @on-close="onCloseProductName"></productModal>
    <!--筛选列组件导入-->
    <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
  </div>
</template>

<script>
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
import utils from '@/utils';
import { queryDictBase } from "@/api/basic/public.js"
import { exportData } from "@/api/public.js";
import { getMinWidth } from "@/utils";
import { getLogicalArea } from "@/api/basic/goodsPosition";
import { getEffectiveWarningPage } from "@/api/drug/warningtoList";
import { columns } from "./config";
import {
  getAllStorageType,
  getAllStorageRoomByType,
  getStorageRoomAreaList,
} from "@/api/basic/goodsPosition.js";
import productModal from "./components/productModal.vue";
export default {
  name: "warningtoList",
  components: { productModal },
  data() {
    return {
      loading: false,
      tableData: [],
      columns: columns(),
      oldTableColumns: JSON.parse(JSON.stringify(columns())),
      tableKey: Date.now(), //表格列刷新flag
      storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
      tablePage: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      btnList: [
        {
          label: "查询",
          type: "primary",
          icon: "el-icon-search",
          code: "btn:wms:warningtoList:getInfo",
          clickEvent: this.searchList,
        },
        {
          label: "导出",
          type: "success",
          icon: "el-icon-download",
          code: "btn:wms:warningtoList:export",
          clickEvent: this.exportHandle,
        },
      ],
      formData: {
        // effDayEnd: "", //近效期天数
        productCode: "",
        productName: "",
        storageTypeCode: "", //库别
        storageRoomCode: "", //库房
        storageAreaCode: "", //库区
        goodsAllocation: "", //货位编码
        batchNumber: "", //批号
        effectiveType: "", //效期类型
        ownerCode: "XYYYXKJGS", //业主编码
        largeCategoryCode: "", //商品大类
      },
      storageTypeCode: [], //库别列表
      storageRoomCode: [], //库房列表
      storageAreaCode: [], //库区列表
      effectiveType: [
        {
          label: "过效期",
          value: "3",
        },
        {
          label: "近效期",
          value: "2",
        },
        {
          label: "预警效期",
          value: "1",
        },
      ], //效期类型列表
      ownerCode: [], // 业主列表
      largeCategoryCode: [
        {
          label: "普通药品",
          value: "200",
        },
        {
          label: "中药",
          value: "201",
        },
        {
          label: "医疗器械",
          value: "202",
        },
        {
          label: "非药",
          value: "203",
        },
        {
          label: "赠品",
          value: "204",
        },
      ], //药品大类列表
    };
  },
  activated() {
    this.$nextTick(() => {
      utils.pageActivated()
      this.getColumWidth('warningtoList', 'columns', 'xTable')
    })
    this.getInfo();
    this.getStorageTypeCode();
    this.getLogicalArea();
    this.searchList();
  },
  methods: {
    // 设置表头筛选列-子组件回传
    setFilterTableHead({ type, fullColumns, tableNo }) {
      this.columns = [...fullColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('warningtoList', 'columns')
      this.$nextTick(() => {
        this.getColumWidth('warningtoList', 'columns', 'xTable')
      })
    },
    //设置筛选列
    setingTableDataHander(index) {
      this.type = index;
      let columns = [];
      columns = JSON.parse(JSON.stringify((this.columns)));
      this.$refs.filterDialog.open(columns, 1, true, 'xTable')
    },
    //重置自定义列宽方法
    //每一个vxe-table单独实现
    resetFilterTableHead(tableNo) {
      this.oldColumns = JSON.parse(JSON.stringify((columns())));
      this.columns = [...this.oldColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('warningtoList', 'oldColumns')
      this.$nextTick(() => {
        this.getColumWidth('warningtoList', 'columns', 'xTable')
      })
    },
    //监测拖动列宽变化方法
    resizableChange({ column }) {
      this.columns[this.columns.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },
    //保存/更新自定义列方法实现
    //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
    // column： 所需要保存的表格绑定的列数据
    setColumnWidth(page, column) {
      const columns = this[column]
      const params = {
        page: page,
        columns: columns
      }
      saveUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },
    //查询自定义列方法实现
    // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
    // column:所需要渲染的表格绑定的列数据
    // table: 所需要渲染的表格的 'ref'
    getColumWidth(page, column, table) {
      const params = {
        page: page,
      }
      queryUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          const columns = result
          //出参只含field和width的匹配
          columns?.forEach(item => {
            //更改对应column的
            this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
            //匹配后端所传列顺序
            const foundItem = this[column].find(d => d.field === item.field)
            if (foundItem) {
              this[column].push(foundItem)
              this[column].splice(this[column].indexOf(foundItem), 1)
            }
          })
          this.storeDone = true //查询到列表格数据标识
          this.tableKey = Date.now() //强制刷新表格列
          // 若返回数据格式存在 field,title,visible,width
          // this[column] = columns
          // this.tableKey = Date.now() 
          // this.storeDone = true
        } else {
          this.storeDone = false
        }
      })
    },

    deleteProductName() {
      this.formData.productCode = "";
      this.formData.productName = "";
    },
    //商品名称搜索
    openProductName() {
      //   console.log("商品名称");
      this.$refs.productModal.open();
    },
    //关闭商品名称查询
    onCloseProductName(row) {
      // console.log(222);
      this.formData.productName = row.productName;
      this.formData.productCode = row.productCode;
    },
    storageTypeChange() {
      if (this.formData.storageTypeCode != "") {
        this.formData.storageRoomCode = "";
        this.storageRoomCode = [];
        this.storageAreaCode = [];
        this.formData.storageAreaCode = "";
        this.getStorageRoomCode();
      }
    },
    storageTypeClear() {
      if (this.formData.storageTypeCode == "") {
        this.formData.storageRoomCode = "";
        this.storageRoomCode = [];
        this.formData.storageAreaCode = "";
        this.storageAreaCode = [];
      }
    },
    storageRoomFocus() {
      if (this.formData.storageTypeCode == "") {
        this.$message.warning("请先选择库别");
        return;
      }
    },
    storageRoomClear() {
      this.formData.storageAreaCode = "";
      this.storageAreaCode = [];
    },
    storageRoomChange() {
      this.storageAreaCode = [];
      this.formData.storageAreaCode = "";
    },
    storageAreaFocus() {
      if (
        this.formData.storageTypeCode == "" &&
        this.formData.storageRoomCode == ""
      ) {
        this.$message.warning("请先选择库别和库房");
        return;
      }
      if (
        this.formData.storageRoomCode != "" &&
        this.formData.storageTypeCode == ""
      ) {
        this.$message.warning("请先选择库别");
        return;
      }
      if (
        this.formData.storageRoomCode == "" &&
        this.formData.storageTypeCode != ""
      ) {
        this.$message.warning("请先选择库房");
        return;
      }
      if (
        this.formData.storageRoomCode != "" &&
        this.formData.storageTypeCode != ""
      ) {
        this.getStorageAreaCode();
      }
    },
    getStorageTypeCode() {
      getAllStorageType().then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.storageTypeCode = result || [];
        } else {
          this.$message.error(msg);
        }
      });
    },
    getStorageRoomCode() {
      const params = {
        str: this.formData.storageTypeCode,
      };
      getAllStorageRoomByType(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.storageRoomCode = result || [];
        } else {
          this.$message.error(msg);
        }
      });
    },
    getStorageAreaCode() {
      const { storageRoomCode, storageTypeCode } = this.formData;
      const params = Object.assign({
        storageRoomCode,
        storageTypeCode,
      });
      getStorageRoomAreaList(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.storageAreaCode = result || [];
        } else {
          this.$message.error(msg);
        }
      });
    },
    //字典查数据
    async getLogicalArea() {
      const res = await queryDictBase({ items: ["YZBM", "SPDL"] });
      if (res.code === 0) {
        this.ownerCode = res.result.YZBM;
        this.largeCategoryCode = res.result.SPDL;
      } else {
        this.$message.error(res.msg);
      }

    },
    //分页
    handlePageChange({ pageSize, currentPage }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.getInfo();
    },
    //背景色
    rowClassName(val) {
      console.log(val);
      if (val.row.warning == 2) {
        return { "background-color": "#fabcbc !important" };
      } else if (val.row.warning == 1) {
        return { "background-color": "#FFC300 !important" };
      } else if (val.row.warning == 3) {
        return { "background-color": "#e36262 !important" };
      } else {
        return "";
      }
    },
    searchList() {
      this.tablePage.pageNum = 1;
      this.getInfo();
    },
    //查询表格数据
    async getInfo() {
      this.loading = true;
      const params = {
        pageNo: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        ...this.formData
      };
      const res = await getEffectiveWarningPage(params);
      if (res.code === 0) {
        this.tableData = res.result.list || [];
        this.tablePage.total = res.result.total;
        this.tablePage.pageNum = res.result.pageNum;
        // this.tablePage.pageSize = res.result.pageSize;
        this.loading = false;
        this.columns = getMinWidth(this.columns, this.tableData, 24, 300, 165);
      } else {
        this.loading = false;
        this.tableData = [];
        this.$message.error(res.mes);
      }
    },
    exportHandle() {
      if (!this.tableData.length) {
        this.$message.warning('无数据可供导出！')
        return
      }
      const formInfo = {

        pageNo: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        ...this.formData
      };

      const colNameDesc = this.columns.filter(item => item.visible).map(item => item.title).join(',')
      const colName = this.columns.filter(item => item.visible).map(item => item.field).join(',')
      const exportParams = JSON.stringify(formInfo)
      const params = {
        moduleName: 'WAREHOUSE',
        menuDesc: '商品近效期预警',
        taskBean: 'WarehouseEffectiveWarningExportTask',
        orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
        warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
        colNameDesc: colNameDesc,
        colName: colName,
        exportParams: exportParams
      }
      // console.log(params, 'params');
      this.$confirm('是否确认导出表单内容？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportData(params).then(res => {
          const { code, msg, result } = res
          if (code === 0 && result) {
            this.$message({
              type: 'success',
              message: '导出成功，请前往下载中心查看！！!'
            })
          } else {
            this.$message.error(msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    }
  }

};
</script>

<style>
:deep(.orange-row) {
  background-color: #fde5e5 !important;
}

:deep(.vxe-table .blue-row) {
  background-color: #fff9dd !important;
}
</style>
