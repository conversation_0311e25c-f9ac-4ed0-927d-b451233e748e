import request from '@/utils/request'
import { formData } from '@/utils/index'
// ----------------------新建收货单接口---------------------------
/**
 *  获取质量复查执行页面信息
 * @param {object} 查询实体
 */
export function getheadRecheckOrder(params) {
  return request({
    url: '/instock/headRecheckOrder/getHeadRecheckOrderDetailByCode',
    method: 'post',
    data: params
  })
}
/**
 *  获取质量复查查询条件
 * @param {object} 查询实体
 */
export function getheadRecheckOrderByCode(data) {
  return request({
    url: '/instock/headRecheckOrder/getHeadRecheckOrderDetailByCode',
    method: 'post',
    data: data
  })
}
/**
 *  获取当前登录用户信息
 * @param {object} 查询实体
 */
export function getCurrentUser(data) {
  return request({
    url: 'purchase/common/getCurrentUserForPurchase',
    method: 'post',
    data: data
  })
}
/**
 *  不合格事项模糊搜索
 * @param {object} 查询实体
 */
export function getReason(params) {
  return request({
    url: '/instock/common/getDictByDictType',
    method: 'post',
    data: params
  })
}
/**
 *  批量上架区域信息
 * @param {object} 查询实体
 */
export function getShelfAreas(data) {
  return request({
    url: 'purchase/common/getStorageTypeAreas',
    method: 'post',
    data: formData(data)
  })
}
/**
 *  上架区域信息
 * @param {object} 查询实体
 */
export function getShelfArea(data) {
  return request({
    url: '/instock/common/getStorageTypeAreas',
    method: 'post',
    data: data
  })
}
/**
 *  获取容器列表
 * @param {object} 查询实体
 */
export function getContainerList(data) {
  return request({
    url: '/instock/common/selectListContainer',
    method: 'get',
    params: data
  })
}
/**
 *  获取提交请求
 * @param {object} 查询实体
 */
export function submitCheck(data) {
  return request({
    url: '/instock/headRecheckOrder/submit',
    method: 'post',
    data: data
  })
}
/**
 *  获取商品校验
 * @param {object} 查询实体
 */
export function productValidityBatchCheck(data) {
  return request({
    url: '/instock/common/productValidityBatchCheck',
    method: 'post',
    data: data
  })
}
/**
 *  获取取消请求
 * @param {object} 查询实体
 */
export function bindHeadCheckOrder(data) {
  return request({
    url: 'purchase/headRecheckOrder/bindOrNotPurchaseHeadRecheckOrderCancel',
    method: 'post',
    data: formData(data)
  })
}
/**
 *  获取取消请求
 * @param {object} 查询实体
 */
export function cancelList(data) {
  return request({
    url: 'purchase/headRecheckOrder/bindOrNotPurchaseHeadRecheckOrderCancel',
    method: 'post',
    data: formData(data)
  })
}

//入库作>质量复查->管理->查询
export function getHeadRecheckOrderList(data) {
  return request({
    url: '/instock/headRecheckOrder/page',
    method: 'post',
    data: data
  })
}

//入库作>质量复查->管理->查询复查单执行页面列表
export function getHeadRecheckOrderListByCode(data) {
  return request({
    url: '/instock/headRecheckOrder/getHeadRecheckOrderDetailByCode',
    method: 'post',
    data: data
  })
}

//入库作>质量复查->管理->质量复查单打印
export function getHeadRecheckOrderPrint(data) {
  return request({
    url: '/instock/purchase/storageOrder/upShelfPrint',
    method: 'post',
    data: data
  })
}

//入库作>质量复查->管理->质量复查单提交操作
export function getHeadRecheckOrderSubmit(data) {
  return request({
    url: '/instock/headRecheckOrder/submit',
    method: 'post',
    data: data
  })
}