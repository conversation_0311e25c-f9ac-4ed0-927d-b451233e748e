import request from "@/utils/request";

//入库完成作业监控
export function getPurchaseMonitor(data) {
  return request({
    url: "instock/monitor/completionReport",
    method: "get",
    params: data,
  });
}

//入库未完成作业监控
export function getPurchaseUnMonitor(data) {
  return request({
    url: "instock/monitor/uncompletionReport",
    method: "get",
    params: data,
  });
}

//采购订单监控
export function getPurchaseOrderMonitor(data) {
  return request({
    url: "instock/monitor/purchaseOrderMonitoringReport",
    method: "get",
    params: data,
  });
}

//验收超时
export function getPurchaseOverTime(data) {
  return request({
    url: "instock/monitor/verifyOvertimeUncompletionReport",
    method: "get",
    params: data,
  });
}

//上架超时
export function getPurchaseShelfOverTime(data) {
  return request({
    url: "instock/monitor/availableOvertimeUncompletionReport",
    method: "get",
    params: data,
  });
}

// 错误修改监控
export function getPurchaseErrorModify(data) {
  return request({
    url: "instock/monitor/getListStorageModifyReportDTO",
    method: "get",
    params: data,
  });
}
