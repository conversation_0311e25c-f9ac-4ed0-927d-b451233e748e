import request from '@/utils/request'

// 销售退回复查单分页列表查询
export function saleBackReviewSearch(data) {
  return request({
    url: '/instock/salesreturn/review/queryReviewListPage',
    method: 'post',
    data
  })
}
// 销售退回复查单详情录入复查结果以及商品明细查询
export function reviewDetailResultSearch(data) {
  return request({
    url: '/instock/salesreturn/review/findReviewAndDetailsByCode',
    method: 'post',
    data
  })
}
// 销售退回复查单确认复查
export function reviewConfirmReview(data) {
  return request({
    url: '/instock/salesreturn/review/submit',
    method: 'post',
    data
  })
}
// 获取不合格品列表
export function getDisqualification(data) {
  return request({
    url: '/instock/common/getDictByDictType',
    method: 'post',
    data
  })
}