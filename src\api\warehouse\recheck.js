import request from '@/utils/request'
import { formData } from '@/utils/index'
// ----------------------新建收货单接口---------------------------
/**
 *  获取复查执行页面信息
 * @param {object} 查询实体
 */
export function getRecheckOrder(params) {
  return request({
    url: 'purchase/recheckOrder/getRecheckOrderDetailByCode',
    method: 'get',
    params: params
  })
}
/**
 *  获取复查查询条件
 * @param {object} 查询实体
 */
export function getRecheckOrderByCode(data) {
  return request({
    url: 'purchase/recheckOrder/getRecheckOrderByCode',
    method: 'post',
    data: data
  })
}
/**
 *  获取当前登录用户信息
 * @param {object} 查询实体
 */
export function getCurrentUser(data) {
  return request({
    url: 'purchase/common/getCurrentUserForPurchase',
    method: 'post',
    data: data
  })
}
/**
 *  绑定，取消绑定
 * @param {object} 查询实体
 */
export function bindCheckOrder(data) {
  return request({
    url: 'purchase/recheckOrder/bindOrNotPurchaseRecheckOrderCancel',
    method: 'post',
    data: formData(data)
  })
}
/**
 *  不合格事项模糊搜索
 * @param {object} 查询实体
 */
export function getReason(params) {
  return request({
    url: 'dictBases/getDictListByNameAndType',
    method: 'get',
    params: params
  })
}
/**
 *  上架区域信息
 * @param {object} 查询实体
 */
export function getShelfArea(data) {
  return request({
    url: '/instock/common/getStorageTypeAreas',
    method: 'post',
    data: data
  })
}
/**
 *  获取容器列表
 * @param {object} 查询实体
 */
export function getContainerList(data) {
  return request({
    url: '/instock/common/pageContainer',
    method: 'post',
    data: data
  })
}
/**post
 *  获取提交请求
 * @param {object} 查询实体
 */
export function submitCheck(data) {
  return request({
    url: 'purchase/recheckOrder/updateRecheckOrderRecheckResult',
    method: 'post',
    data: data
  })
}
/**
 *  获取商品校验
 * @param {object} 查询实体
 */
export function productValidityBatchCheck(data) {
  return request({
    url: 'master/productValidityConfig/productValidityBatchCheck',
    method: 'post',
    data: data
  })
}
/**
 *  获取取消请求
 * @param {object} 查询实体
 */
export function cancel(data) {
  return request({
    url: 'purchase/recheckOrder/bindOrNotPurchaseRecheckOrderCancel',
    method: 'post',
    data: formData(data)
  })
}
