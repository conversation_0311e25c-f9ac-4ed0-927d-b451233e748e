import request from '@/utils/request';
import { param } from '../../utils';
/************基础资料************* */
/**
 * 基础资料-货位档案-商品与货位关系维护
 */

//库别字段
export function getAllStorageType() {
    return request({
        url: '/basicdata/dictBases/getAllStorageType',
        method: 'post',
    })
}

//联动库别查询所有库房
export function getAllStorageRoomByType(param) {
    return request({
        url: '/basicdata/dictBases/getAllStorageRoomByType',
        method: 'post',
        data: param
    })
}

//分页查询
export function getAllProductRelation(param) {
    return request({
        url: '/basicdata/product/logicalRegion/page',
        method: 'post',
        data: param
    })
}

//获取商品大类
export function getAllProductBigType(query) {
    return request({
        url: '/basicdata/dictBases/getByDictType',
        method: 'get',
        params: query
    })
}

//获取商品详情
export function getProductDetail(id) {
    return request({
        url: '/basicdata/product/logicalRegion/getById',
        method: 'post',
        data: id
    })
}

//更新商品与货位关系
export function updateProductRelation(param) {
    return request({
        url: '/basicdata/product/logicalRegion/update',
        method: 'post',
        data: param
    })
}

//模板下载
export function downloadTemplate() {
    const fileName = '商品逻辑关系.xlsx';
    return request({
        url: `https://files.test.ybm100.com/B2BCOS/WmsCloud/basic/${fileName}`,
        method: 'get',
        responseType: 'blob'
    })
}

//货位与商品维护关系文件上传
export function uploadProductRelation(param) {
    return request({
        url: '/basicdata/product/logicalRegion/import',
        method: 'post',
        data: param
    })
}

//打印货位编号
export function printProductRelation(param) {
    return request({
        url: '/basicdata/goodsPosition/print',
        method: 'post',
        data: param
    })
}

//商品已绑定逻辑区查询
export function getBindLogicalRegion(data) {
    return request({
        url: '/basicdata/product/logicalRegion/selectProductLogicalRegions',
        method: 'post',
        data: data
    })
}