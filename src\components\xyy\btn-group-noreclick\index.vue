<template>
  <div class="clearfix">
    <el-row>
      <template v-for="(item, index) in btnListCopy">
        <button
          v-if="item.shortkey"
          :key="item.label"
          :disabled="item.disabled || item.loading"
          :loading="item.loading"
          :icon="item.icon"
          type="button"
          :class="{ btn: true, 'is-loading': item.loading }"
          @click="clickBtn(item,index)"
        >
          <span class="sMag">{{ item.label }}</span>
          <span class="sMag">{{ item.shortkey }}</span>
          <i v-if="item.loading" class="btn-loading el-icon-loading" />
        </button>

        <el-button
          v-else
          :key="item.label"
          :type="item.type"
          :disabled="item.disabled"
          :loading="item.loading"
          class="el-btn"
          :icon="item.icon"
          @click="clickBtn(item,index)"
          >{{ item.label }}</el-button
        >
      </template>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "NoBtnGroup",
  props: {
    btnList: {
      // 按钮列表配置
      type: Array,
      default: () => [],
    },
    useEvent: {
      // 是否启用按钮组快捷键
      type: Boolean,
      default: false,
    },
    excuseEvent: {
      // 是否执行快捷键事件
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      btnListCopy: this.btnList,
    };
  },
  mounted() {
    if (!this.useEvent) return false;
    const hotkeys = this.getHotkeys().toLowerCase();
    this.$hotkeys(hotkeys, this.handlerKeys);
  },
  beforeDestroy() {
    // 销毁快捷键
    const hotkeys = this.getHotkeys().toLowerCase();
    this.$hotkeys.unbind(hotkeys);
  },
  methods: {
    clickBtn(item, index) {
      setTimeout(() => {
        this.stopLoading(item, index);
      }, 1000);
      this.startLoading(item, index);
      item.clickEvent(item);
    },
    handlerKeys(event, handler) {
      event.preventDefault();
      const key = handler.key;
      this.btnList.forEach((el) => {
        if (el.shortkey.toLowerCase() === key && this.excuseEvent) {
          el.clickEvent(el, event);
        }
      });
    },
    getHotkeys() {
      const hotkeys = [];
      this.btnList.forEach((el) => {
        if (el.shortkey) {
          hotkeys.push(el.shortkey);
        }
      });
      return hotkeys.join(",");
    },
    //开始loading
    startLoading(item, index){
      let btncopy = _.cloneDeep(this.btnListCopy);
      this.btnListCopy = [];
      btncopy[index].loading = true;
      this.btnListCopy = btncopy;
    },
    //开始停止
    stopLoading(item, index){
      let btncopy = _.cloneDeep(this.btnListCopy);
      this.btnListCopy = [];
      btncopy[index].loading = false;
      this.btnListCopy = btncopy;
    }
  },
};
</script>

<style lang="scss" scoped>
.clearfix {
  float: right;
  .btn {
    position: relative;
    height: 32px;
    color: #fff;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    vertical-align: top;
    touch-action: manipulation;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    background-color: #2db7f5;
    border-color: #2db7f5;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.42857143;
    border-radius: 4px;
    font-size: 14px;
    min-width: 54px;
    padding: 0 12px;
    margin-right: 10px;
    .btn-loading {
      position: relative;
      top: -18px;
      color: #2db7f5;
    }
    &.is-loading::before {
      pointer-events: none;
      content: "";
      position: absolute;
      left: -1px;
      top: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: inherit;
      // background-color: rgba(0, 0, 0, 0.6);
    }
    &:disabled {
      cursor: not-allowed;
      background-color: #a0cfff;
      border-color: #a0cfff;
    }
    &:last-child {
      margin-right: 0px;
    }
    .sMag {
      display: inline-block;
      height: 15px;
      line-height: 15px;
      font-size: 12px;
    }
  }
  .el-button {
    margin-right: 10px;
  }
}</style
>>
