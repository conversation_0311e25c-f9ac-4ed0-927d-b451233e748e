import request from '@/utils/request'

/**
 * 校验工号是否正确
 * @param {object} 查询实体
 */
export function checkJobNumber(params) {
  return request({
    url: '/outstock/web/outstock/whole/picking/findEmpNumber',
    method: 'get',
    params: params
  })
}

/**
 * 工号是否有任务占用
 * @param {object} 查询实体
 */
export function getJobStateByJobNumber(params) {
  return request({
    url: 'pickingTask2BController/getJobStateByJobNumber',
    method: 'post',
    data: params
  })
}

/**
 * 工号下的拣货任务列表
 * @param {object} 查询实体
 */
export function getPickTaskByJobNumber(params) {
  return request({
    url: 'pickingTask2BController/getPickTaskByJobNumber',
    method: 'post',
    data: params
  })
}

/**
 * 异常处理按钮
 * @param {object} 查询实体
 */
export function exceptionDeal(params) {
  return request({
    url: 'pickingTask2BController/exceptionDeal',
    method: 'post',
    data: params
  })
}

/**
 * 拣货确认按钮
 * @param {object} 查询实体
 */
export function pickConfirmationTask(params) {
  return request({
    url: 'pickingTask2BController/pickConfirmationTask',
    method: 'post',
    data: params
  })
}

/**
 * 电商任务列表展示（） 正在拣货批拣单出库单数/待领批拣单出库单数/补货挂起批拣单出库单数
 * @param {object} 查询实体
 */
export function getPickContinfo(params) {
  return request({
    url: 'pickingTask2BController/getPickContinfo',
    method: 'post',
    data: params
  })
}

/**
 * 校验批拣单(弹框)
 * @param {object} 查询实体
 */
export function getStateByBatchInspectionCode(params) {
  return request({
    url: 'pickingTask2BController/getStateByBatchInspectionCode',
    method: 'post',
    data: params
  })
}

/**
 * 校验小车(弹框)
 * @param {object} 查询实体
 */
export function checkContainerCode(params) {
  return request({
    url: 'pickingTask2BController/checkContainerCode',
    method: 'post',
    data: params
  })
}

/**
 * 绑定任务(弹框)
 * @param {object} 查询实体
 */
export function bindTask(params) {
  return request({
    url: 'pickingTask2BController/bindTask',
    method: 'post',
    data: params
  })
}
