import request from "@/utils/request";

//零货打印列表
export function getListPickingTask(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listPickingTask",
        method: "post",
        data: data, 
    });
}
//零货打印详情列表
export function getPickingtaskprint(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listSalesOrder",
        method: "post",
        data: data, 
    });
}

//药检报告打印/下载
export function getPickingtaskprintDownload(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listDrugTestReportPrint",
        method: "post",
        data: data, 
    });
}

//随货同行单打印/下载/预览
export function getPickingtaskprintFollow(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listFollowPrint",
        method: "post",
        data: data,
    })
}

//获取随货同行单空模板列表
export function getPickingtaskprintFollowEmpty(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listFollowTemplate",
        method: "post",
        data: data,
    })
}

//随货同行单空模板打印
export function getPickingtaskprintFollowEmptyPrint(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listFollowTemplatePrint",
        method: "post",
        data: data,
    })
}

//打印管理—历史单据打印
export function getPickingtaskprintHistory(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/getPrint",
        method: "post",
        data: data,
    })
}

//打印管理—整件订单打印列表
export function getPickingtaskprintOrder(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listWholePickingTask",
        method: "post",
        data: data,
    })
}

//销售订单——订单详情
export function getPickingtaskprintOrderDetail(data){
    return request({
        url: "/outstock/web/outstock/salesorder/detail",
        method: "post",
        data: data,
    })
}

//打印管理—面单打印
export function getPickingtaskprintSheet(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/listExpressSheetPrint",
        method: "post",
        data: data,
    })
}

//打印管理—出库单详情
export function getPickingtaskprintSheetDetail(data){
    return request({
        url: "/outstock/web/outstock/wave/pickingtaskprint/getSalesOrderDetail",
        method: "post",
        data: data,
    })
}

//打印管理-PO单打印
export function printPO(params) {
    return request({
        url: 'outstock/web/outstock/remarkPrint/listFollowPrint',
        method: 'post',
        data: params
    })
}