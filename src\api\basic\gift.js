import request from '@/utils/request'

/************基础资料 start************* */
/**
 * 基础资料-客户资料-列表
 */
export function productGiftsPageQuery(param) {
    return request({
        url: '/basicdata/product/gifts/pageQuery',
        method: 'post',
        data:param
    })
}

/**  礼品规则查询*/
export function queryTableData(params) {
  return request({
    url: '/basicdata/product/gifts/pageQuery',
    method: 'post',
    data: params
  })
}

/**  礼品规则保存*/
export function addGift(params) {
    return request({
        url: '/basicdata/product/gifts/addGifts',
        method: 'post',
        data: params
    })
}

/**  礼品规则修改*/
export function editGift(params) {
    return request({
        url: '/basicdata/product/gifts/updateGifts',
        method: 'post',
        data: params
    })
}

/**  礼品规则详情*/
export function getGiftDetail(params) {
  return request({
      url: '/basicdata/product/gifts/productGifts',
      method: 'post',
      data: params
  })
}