<template>
  <el-switch
    :value="value"
    :width="75"
    :active-text="activeText"
    :inactive-text="inactiveText"
    :active-color="activeColor"
    :inactive-color="inactiveColor"
    :active-value="activeValue"
    :inactive-value="inactiveValue"
    @change="changeFuntion"
    :disabled="getRoles()"
  />
</template>


<script>
export default {
  name: "XyySwitch",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    //通过这里控制是不是需要菜单控制
    noDisabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [Boolean, String, Number],
      default: 0,
    },
    activeText: {
      type: String,
    },
    inactiveText: {
      type: String,
    },
    activeColor: {
      type: String,
    },
    inactiveColor: {
      type: String,
    },
    activeValue: {
      type: [Boolean, String, Number],
      default: true,
    },
    inactiveValue: {
      type: [Boolean, String, Number],
      default: false,
    },
  },
  methods: {
    changeFuntion(value) {
      this.$emit("change", value);
    },
    getRoles() {
      if (this.noDisabled) {
        return true;
      }
      let roles = window.syncButtons[this.$route.meta.title];
      if(!roles){
        return true;
      }
      let rolesTitles = roles.map((item) => {
        return item.actionName;
      });
      let qx = rolesTitles.find((item) => {
        return (
          item.indexOf("生产属性") !== -1 ||
          item.indexOf("考勤状态") !== -1 ||
          item.indexOf("生产") !== -1 ||
          item.indexOf("非生产") !== -1 ||
          item.indexOf("启用") !== -1 ||
          item.indexOf("停用") !== -1 ||
          item.indexOf("是") !== -1 ||
          item.indexOf("否") !== -1
        );
      });
      return !qx;
    },
  },
};
</script>



<style lang="scss">
.el-switch__label {
  position: absolute;
  display: none;
  font-size: 10px !important;
  color: #fff !important;
}
.el-switch__label * {
  font-size: 10px !important;
}
/*打开时文字位置设置*/
.el-switch__label--right {
  z-index: 1;
  right: 23px; // 这里是重点
  top: 0.5px;
}
/*关闭时文字位置设置*/
.el-switch__label--left {
  z-index: 1;
  left: 28px; // 这里是重点
  top: 0.5px;
}
/*显示文字*/
.el-switch__label.is-active {
  display: block;
}
.el-switch__core {
  width: 74px;
  height: 22px;
  border: 2px solid #dcdfe6;
  border-radius: 13px;
}
</style>