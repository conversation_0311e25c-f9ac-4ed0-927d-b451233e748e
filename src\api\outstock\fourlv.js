import request from '@/utils/request'

/**
 *   运费规则-省市区地址联动
 * @param {object} 
 */
 export function getNextAreaListByCode(data) {
    return request({
      url: 'basicdata/dictView/getDictAreaByPcode',
      method: 'post',
      data: data
    })
}

/**
 *   省市区地址联动-查询
 * @param {object} 
 */
 export function findData(data) {
  return request({
    url: 'epidemicControl/findData',
    method: 'post',
    data: data
  })
}

/**
 *   省市区地址联动-增加管控
 * @param {object} 
 */
 export function addControlData(data) {
  return request({
    url: 'epidemicControl/addControlData',
    method: 'post',
    data: data
  })
}

/**
 *   省市区地址联动-减少管控
 * @param {object} 
 */
 export function removeControlData(data) {
  return request({
    url: 'epidemicControl/removeControlData',
    method: 'post',
    data: data
  })
}