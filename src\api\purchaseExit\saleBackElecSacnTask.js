import request from "@/utils/request";

//追溯码扫描-分页查询
export function getSaleBackElecScanTaskList(params) {
  return request({
    url: "/instock/salesreturn/scan/findSupervisionList",
    method: "post",
    data: params,
  });
}

//追溯码扫描-扫描商品信息
export function getSaleBackElecScanTaskInfo(params) {
  return request({
    url: "/instock/salesreturn/scan/queryScannedNumber",
    method: "post",
    data: params,
  });
}

//追溯码查询
export function getSaleBackElecScanTaskTrace(params) {
  return request({
    url: "/instock/common/queryOrderDrugRegulatoryCode ",
    method: "post",
    data: params,
  });
}

//追溯码扫描-提交
export function getSaleBackElecScanTaskConfirm(params) {
  return request({
    url: "/instock/salesreturn/scan/scanProducts",
    method: "post",
    data: params,
  });
}

//追溯码扫描-删除
export function getSaleBackElecScanTaskDelete(params) {
  return request({
    url: "/instock/purchase/codeScanOrder/deleteOrderDrugRegulatoryCode",
    method: "post",
    data: params,
  });
}

//追溯码扫描-解锁
export function getSaleBackElecScanTaskUnlock(params) {
  return request({
    url: "/instock/salesreturn/scan/unlockedProduct",
    method: "post",
    data: params,
  });
}


