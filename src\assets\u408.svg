﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="27px" height="28px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="43px" y="111px" width="27px" height="28px" filterUnits="userSpaceOnUse" id="filter210">
      <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.6980392156862745 0  " in="shadowComposite" />
    </filter>
    <g id="widget211">
      <path d="M 55.5 115  C 60.26 115  64 118.96  64 124  C 64 129.04  60.26 133  55.5 133  C 50.74 133  47 129.04  47 124  C 47 118.96  50.74 115  55.5 115  Z " fill-rule="nonzero" fill="#f59a23" stroke="none" />
      <path d="M 55.5 115.5  C 59.980000000000004 115.5  63.5 119.24  63.5 124  C 63.5 128.76  59.980000000000004 132.5  55.5 132.5  C 51.019999999999996 132.5  47.5 128.76  47.5 124  C 47.5 119.24  51.019999999999996 115.5  55.5 115.5  Z " stroke-width="1" stroke="#ffffff" fill="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -43 -111 )">
    <use xlink:href="#widget211" filter="url(#filter210)" />
    <use xlink:href="#widget211" />
  </g>
</svg>