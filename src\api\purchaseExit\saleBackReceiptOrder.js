import request from "@/utils/request";

/**
 * 销售退回-销售退回收货单查询
 */
export function getQueryReceiveListPage(param) {
  return request({
    url: "/instock/salesreturn/receive/queryReceiveListPage",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-销售退回收货单明细查询
 */
export function findReceiveByReceiveCode(param) {
  return request({
    url: "/instock/salesreturn/receive/findReceiveByReceiveCode",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-销售退回收货单明细表单查询
 */
export function queryPushStockProcess(param) {
  return request({
    url: "/instock/salesreturn/receive/queryReceiveDetailPage",
    method: "post",
    data: param,
  });
}

/**
 * 销售退回-确认收货
 */
export function confirmReceive(param) {
  return request({
    url: "/instock/salesreturn/receive/confirmReceive",
    method: "post",
    data: param,
  });
}
