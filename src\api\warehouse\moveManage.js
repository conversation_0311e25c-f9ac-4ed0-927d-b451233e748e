import request from '@/utils/request'

/**
 *  移库单
 * @param {object} 查询实体
 */
export function apiQueryMoveOrderList(params) {
    return request({
      url: 'warehouse/warehouseMovement/findWarehouseMovementList',
      method: 'post',
      data: params
    })
}
/**
 *  移库单-详情
 * @param {object} 查询实体
 */
export function apiQueryMoveOrderDetail(params) {
  return request({
    url: 'warehouse/warehouseMovement/getMovementBillByNo',
    method: 'post',
    data: params
  })
}
/**
 *  移库质量单审批单
 * @param {object} 查询实体
 */
export function apiQueryMoveQualityOrderList(params) {
    return request({
      url: 'warehouse/warehouseMovement/findWarehouseApprovalList',
      method: 'post',
      data: params
    })
}
/**
 *  新增移库-移入库房
 * @param {object} 查询实体
 */
export function apiQueryMovementIntoStoages(params) {
  return request({
    url: '/warehouse/warehouseMovement/storages',
    method: 'post',
    data: params
  })
}
/**
 *  新增移库-移出库房
 */
export function apiQueryOutStorages(){
  return request({
    url: '/warehouse/warehouseMovement/outStorages',
    method: 'post',
    data: {}
  })
}
/**
 * 新增移库-头部数据
 */
export function apiQueryMovementAddHeader(params) {
  return request({
    url: '/warehouse/warehouseMovement/getMovementNo',
    method: 'post',
    data: params
  })
}
/**
 * 新增移库-商品列表
 */
export function apiQueryMovementAddProduct(params) {
  return request({
    url: '/warehouse/warehouseMovement/findGoodsList',
    method: 'post',
    data: params
  })
}
/**
 * 新增移库单
 */
export function apiMovementAdd(params) {
  return request({
    url: '/warehouse/warehouseMovement/addWarehouseMovementAdDetail',
    method: 'post',
    data: params
  })
}
/**
 * 移库审批单详情
 */
export function apiMoveQuailtyOrderDetail(params) {
  return request({
    url: 'warehouse/warehouseMovement/getWarehouseApprovalByNo',
    method: 'post',
    data: params
  })
}

/**
 * 移库单审批 保存
 */
export function apiMoveQuailtyOrdertBill(params) {
  return request({
    url: 'warehouse/warehouseMovement/approvalMovementBill',
    method: 'post',
    data: params
  })
}

/**
 * 确认移库
 */
export function apiMovementProductAll(params) {
  return request({
    url: 'warehouse/warehouseMovement/movementProductAll',
    method: 'post',
    data: params
  })
}

//移库单打印
export function apiMovementPrint(params) {
  return request({
    url: 'warehouse/warehouseMovement/printMovement',
    method: 'post',
    data: params
  })
}

//新增移库单获取移入整件包装数
export function apiGetMovementWholePackage(params) {
  return request({
    url: '/warehouse/warehouseMovement/getZJKProduct',
    method: 'post',
    data: params
  })
}

//新增移库单文件批量导入
export function apiMovementImport(params) {
  return request({
    url: '/warehouse/warehouseMovement/batchImportMovement',
    method: 'post',
    data: params
  })
}

/**
 * 移库原因
 */
export function apiMoveReason(params){
  return request({
    url: '/warehouse/warehouseMovement/getReasonForRelocationByRoomCode',
    method: 'get',
    params: params,
  })
}