<template>
    <div class="app-container">
        <xyy-panel title="查询条件">
            <btn-group slot="tools" :btn-list="btnList" />
            <el-form ref="formData" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="8" :md="8">
                        <el-form-item label="客户名称/编码">
                            <el-input v-model.trim="formData.clientCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="制单日期">
                            <el-date-picker v-model="formData.orderTime" value-format="yyyy-MM-dd"
                                type="date"
                                :picker-options="pickerOptions" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="销售订单列表">
            <!--筛选列组件-->
            <div slot="tools" style="float:right;">
                <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
                    <svg aria-hidden="true"
                        style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
                        viewBox="0 0 1024 1024">
                        <!-- # + iconfont项目内symbol图标代码 -->
                        <use xlink:href="#icon-headxitongguanli" />
                    </svg>
                </el-button>
            </div>
            <div v-table class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="auto"
                    :data="tableData" @cell-dblclick="handleRowDblClick" resizable @resizable-change="resizableChange" :columns="tableColumn"
                    :key="tableKey">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template v-for="item in tableColumn">
                        <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
                            :min-width="item.width">
                            <template #default="{ row }">
                                <span v-if="item.field === 'orderTime'">
                                    {{ orderTimeInit(row[item.field]) }}
                                </span>
                                <span v-else>{{ row[item.field] }}</span>
                            </template>
                        </vxe-table-column>
                    </template>
                    <!-- <vxe-table-column field="option" title="操作" min-width="150">
                        <template slot-scope="{row}">
                        <div >
                            <el-button type="text" size="small" class="el-btn" @click="inputResults(row)">面单确认</el-button>
                        </div>
                        </template>
                    </vxe-table-column> -->
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <!--筛选列组件导入-->
        <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
    </div>
</template>

<script>
import utils from '@/utils'
import { majorClientsJobProgressMonitoring ,majorClientsConfirm} from '@/api/jobMonitor/largeCustomersMonitor.js'
import XEUtils from 'xe-utils'
import { formatDateRange } from "@/utils/index.js"
import { tableColumns } from './config.js';
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
import { exportData } from '../../../api/public.js';
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
import dayjs from 'dayjs'
export default {
    name: 'largeCustomersMonitor',
    data() {
        return {
            btnList: [
                {
                    label: '查询',
                    type: 'primary',
                    icon: 'el-icon-search', 
                    clickEvent: this.searchList,
                    code: 'btn:wms:largeCustomersMonitor:search'
                },
            ],
            formData: {
                clientCode: "",
                orderTime: defaultEndTime, //制单日期
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            loading: false,
            tableData: [],
            tablePage: {
                pageNum: 1,
                pageSize: 100, //默认100条
                total: 0
            },
            tableColumn: tableColumns(),
            oldTableColumn: JSON.parse(JSON.stringify(tableColumns())),
            tableKey: Date.now(), //表格列刷新flag
            storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
        }
    },
    activated() {
        this.$nextTick(()=>{
            utils.pageActivated()
        })
        this.searchList()
        this.getColumWidth('largeCustomersMonitor', 'tableColumn', 'xTable')
    },
    methods: {
        /**处理时间 */
        orderTimeInit(time) {
            if (time) {
                return dayjs(time).format('YYYY-MM-DD')
            } else {
                return ''
            }
        },
        //面单确认
        inputResults(row) {
            let params = new FormData();
            params.append('expressCode', row.expressCode);
            majorClientsConfirm(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.$message.success(msg);
                    this.getList()
                } else {
                    this.$message.error(msg)
                }
            }).catch(err => {
                this.$message.error('面单确认失败，请稍后再试')
            })
        },
        //跳转详情
        handleRowDblClick({ row }) {
            const queryParams = { 
                clientCode: row.clientCode, 
                orderTime: this.formData.orderTime,
                expressCode: row.expressCode,
                clientName: row.clientName,
                shippingAddress: row.shippingAddress,
            };
            this.$router.push({ path: "/jobMonitor/largeCustomeresDtail", query: queryParams });
        },
        exportHandle() {
            if (this.tableData.length == 0) {
                this.$message.warning('暂无数据导出')
                return
            }
            const { pageNum, pageSize } = this.tablePage
            const [beginDate, endDate] = formatDateRange(this.formData.SubmissionTime)
            const formInfo = {
                pageNum: pageNum,
                pageSize: pageSize,
                ...this.formData
            }
            const colNameDesc = this.tableColumn.filter(item =>
                item.visible == true
            ).map(item => item.title).join(',')
            const colName = this.tableColumn.filter(item =>
                item.visible == true
            ).map(item => item.field).join(',')
            const exportParams = JSON.stringify(formInfo) //查询条件JSON化
            const params = {
                moduleName: 'WAREHOUSE',
                menuDesc: '作业进度监控',
                taskBean: 'OUTSTOCK_jobProgressMonitoring',
                orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
                warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
                colNameDesc: colNameDesc,
                colName: colName,
                exportParams: exportParams
            }
            // console.log(params, 'params');

            //若导出包含导出列弹窗功能则不需要加
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportData(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        getList() {
            this.loading = true
            const { pageNum, pageSize } = this.tablePage
            const params = {
                pageNum: pageNum,
                pageSize: pageSize,
                ...this.formData
            }
            try{
                majorClientsJobProgressMonitoring(params).then(res => {
                    this.loading = false
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.tableData = result.list || []
                        this.tablePage.pageNo = result.pageNo || 1
                        this.tablePage.total = parseInt(result.total)
                    }  else {
                        this.tableData = []
                        this.tablePage.pageNo = 1
                        this.tablePage.total = 0
                        this.$message.error(msg)
                    }
                })
            }catch(err){
                this.loading = false
            }
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        //查询自定义列方法实现
        // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
        // column:所需要渲染的表格绑定的列数据
        // table: 所需要渲染的表格的 'ref'
        getColumWidth(page, column, table) {
            const params = {
                page: page,
            }
            queryUserCustomColumn(params).then(res => {
                const { code, msg, result } = res
                if (code === 0 && result) {
                    const columns = result
                    //出参只含field和width的匹配
                    columns.forEach(item => {
                        //更改对应column的
                        this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
                        //匹配后端所传列顺序
                        const foundItem = this[column].find(d => d.field === item.field)
                        if (foundItem) {
                            this[column].push(foundItem)
                            this[column].splice(this[column].indexOf(foundItem), 1)
                        }
                    })
                    this.storeDone = true //查询到列表格数据标识
                    this.tableKey = Date.now() //强制刷新表格列
                    // 若返回数据格式存在 field,title,visible,width
                    // this[column] = columns
                    // this.tableKey = Date.now() 
                    // this.storeDone = true
                } else {
                    this.storeDone = false
                }
            })
        },

        //保存/更新自定义列方法实现
        //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
        // column： 所需要保存的表格绑定的列数据
        setColumnWidth(page, column) {
            const columns = this[column]
            const params = {
                page: page,
                columns: columns
            }
            saveUserCustomColumn(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.$message.success(msg)
                } else {
                    this.$message.error(msg)
                }
            })
        },
        //监测拖动列宽变化方法
        resizableChange({ column }) {
            this.tableColumn[this.tableColumn.findIndex(item => item.title === column.title)].width = column.resizeWidth
        },
        //重置自定义列宽方法
        //每一个vxe-table单独实现
        resetFilterTableHead(tableNo) {
            this.oldTableColumn = JSON.parse(JSON.stringify((tableColumns())));
            this.tableColumn = [...this.oldTableColumn]
            this.tableKey = Date.now()
            this.setColumnWidth('largeCustomersMonitor', 'oldTableColumn')
            this.getColumWidth('largeCustomersMonitor', 'tableColumn', 'xTable')
        },
        //设置筛选列
        setingTableDataHander(index) {
            this.type = index;
            let columns = [];
            columns = JSON.parse(JSON.stringify((this.tableColumn)));
            this.$refs.filterDialog.open(columns, 1, true, 'xTable')
        },
        // 设置表头筛选列-子组件回传
        setFilterTableHead({ type, fullColumns, tableNo }) {
            this.tableColumn = [...fullColumns]
            this.tableKey = Date.now()
            this.setColumnWidth('largeCustomersMonitor', 'tableColumn')
            this.getColumWidth('largeCustomersMonitor', 'tableColumn', 'xTable')
        },
    },
}
</script>

<style></style>