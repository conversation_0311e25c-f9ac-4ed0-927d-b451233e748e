import request from "@/utils/request";

/**
 * 零货任务索取查询
 * @param {Object} params
 */
export function findWeightItemDetil(params) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/queryTaskList",
    method: "post",
    data: params,
  });
}
/**
 * 零货任务列表汇总查询
 * @param {Object} params 
 * @returns 
 */
export function queryStatistics(params) {
  return request({
    url: "/paperPicking/parts/queryStatistics",
    method: "post",
    data: params,
  });
}

/**
 * 零货索取任务
 * @param {Object} params 
 * @returns 
 */
export function queryAskFor(params) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/askFor",
    method: "post",
    data: params,
  });
}

/**
 * 拣货确认
 * @param {Object} params 
 * @returns 
 */
export function queryPickingConfirm(params) {
  return request({
    url: "/outstock/web/outstock/partPicking/paperPickingList/pickingConfirm",
    method: "post",
    data: params,
  });
}

/**
 * 重新打印拣货单
 * @param {Object} params 
 * @returns 
 */
export function queryReprintPickingList(params) {
  return request({
    // /outstock/web/outstock/partPicking/paperPickingList/reprintPickingList
    url: "/outstock/web/outstock/frontWarehousePicking/reprintPickPrint",
    method: "post",
    data: params,
  });
}
/**
 * 重新打印面单
 * @param {Object} params 
 * @returns 
 */
export function queryReprintFaceList(params) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/reprintFaceList",
    method: "post",
    data: params,
  });
}
