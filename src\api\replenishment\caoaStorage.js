import request from "@/utils/request";

//查询库容补货
export function getCaoaStorage(data) {
  return request({
    url: "warehouse/replenish/findCapReplenishProducts",
    method: "post",
    data: data,
  });
}

//保存库容补货
export function saveCaoaStorage(data) {
  return request({
    url: "warehouse/replenish/addCapReplenish",
    method: "post",
    data: data,
  });
}

//获取业务类型枚举
export function getBusinessTypeEnum(data) {
  return request({
    url: "/warehouse/replenish/findChannelCodes",
    method: "get",
    data: data,
  });
}
