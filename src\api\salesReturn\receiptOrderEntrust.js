import request from '@/utils/request'

/**
 * 查询业主集合
 * @param {*} dictType 业主编码（业主类型：YZBM）
 */
export function getDictCodeType(dictType) {
  return request({
    url: 'salesreturnCommon/getOwnerInfo',
    method: 'post',
    data: { dictType }
  })
}
/**
 *  商品名称模糊查询
 * @param {object} 查询实体
 */
export function findProductPullTips(data) {
  return request({
    url: 'sales/commonSerachProduct',
    method: 'post',
    data: data
  })
}
/**
 *  销售退回收货单查询委托-列表查询
 * @param {object} 查询实体
 */
export function queryReceiveListPage(data) {
  return request({
    url: 'salesreturnReceive/queryJsReceiveListPage',
    method: 'post',
    data: data
  })
}

/**
 *  销售退回收货单查询委托-导出
 * @param {object} 查询实体
 */
export function getExportReceive(data) {
  return request({
    url: '/salesreturnReceive/exportReceive',
    method: 'post',
    data: data
  })
}
