import request from '@/utils/request'

/**
 *  货位调整单列表
 * @param {object} 查询实体
 */
export function apiSimplePositionAdjustList(params) {
    return request({
      url: 'warehouse/locationMovement/findLocationMovementList',
      method: 'post',
      data: params
    })
}
/**
 *  货位调整单详情
 * @param {object} 查询实体
 */
export function apiSimplePositionAdjustDetail(params) {
  return request({
    url: 'warehouse/locationMovement/getLocationDetailByMovementId',
    method: 'post',
    params: params
  })
}
/**
 *  同品同批多货位调整单列表
 * @param {object} 查询实体
 */
export function apiCombinationPositionAdjust(params) {
  return request({
    url: 'warehouse/locationMovement/getGoodsAllocationBySameProSameBat',
    method: 'post',
    data: params
  })
}
/**
 * 同品同批货位调整单详情
 */
export function apiCombinationPositionAdjustDetail(params){
  return request({
    url: 'warehouse/locationMovement/getSameProSameBatList',
    method: 'post',
    data: params
  })
}
/**
 * 同品同批货位调整保存
 */
export function apiSameSkuLocationSave(params){
  return request({
    url: 'warehouse/locationMovement/sameSkuLocation/save',
    method: 'post',
    data: params
  })
}
/**
 * 新增货位调整商品弹窗列表
 */
export function apiGetPositionAdjustGoodsList(params){
  return request({
    url: 'warehouse/locationMovement/findGoodsList',
    method: 'post',
    data: params
  })
}

/**
 * 新增货位获取货位调整单号
 */
export function apiGetLocationMovementNo(params){
  return request({
    url: 'warehouse/locationMovement/LocationFrom',
    method: 'post',
    data: params
  })
}

/**
 * 货位调整库房接口
 */
export function apiWarehouseMovementOutStorage(){
  return request({
    url: 'warehouse/warehouseMovement/outStorages',
    method: 'post',
    data: {}
  })
}

/**
 * 新增货位调整单
 */
export function apiMovementAdd(params) {
  return request({
    url: 'warehouse/locationMovement/addLocationMovementAdDetail',
    method: 'post',
    data: params
  })
}


/**
 * 同品同批货位调整-根据库别查询库房
 */
export function queryStorageRoomByType(params) {
  return request({
    url: 'basicdata/storageRoomArea/findStorageRoomCode',
    method: 'post',
    params: params
  })
}

/**
 * 货位调整单详情-打印货位调整单
 * */
export function apiPrintLocationMovement(params) {
  return request({
    url: 'warehouse/locationMovement/printLocationMovementBillView',
    method: 'post',
    data: params
  })
}