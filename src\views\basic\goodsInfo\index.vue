<template>
  <div>
    <el-card shadow="never" class="form-card">
      <el-row :gutter="20" class="head-line">
        <el-col :lg="6" :md="6">
          <span>商品管理</span>
        </el-col>
        <el-col :lg="18" :md="18" class="side-right">
          <el-button v-if="getPermission('btn:wms:productBase:import')" type="warning"
            @click="importRegulatory">批量维护监管信息</el-button>
          <el-button v-if="getPermission('btn:wms:productBase:search')" type="warning"
            @click="importProfitGoods">批量维护中包装
          </el-button>
          <el-button v-if="getPermission('btn:wms:productBase:search')" type="primary" :loading="searchLoading"
            @click="searchHandler">查询
            <svg aria-hidden="true" style="
                width: 1em;
                height: 1em;
                position: relative;
                top: 2px;
                fill: currentColor;
                overflow: hidden;
              " viewBox="0 0 1024 1024">
              <!-- # + iconfont项目内symbol图标代码 -->
              <use xlink:href="#icon-headchaxun1" />
            </svg>
          </el-button>
          <el-button type="success" v-if="getPermission('btn:wms:productBase:outExcel')" @click="exportHandler">导出
            <svg aria-hidden="true" style="
                width: 1em;
                height: 1em;
                position: relative;
                top: 2px;
                fill: currentColor;
                overflow: hidden;
              " viewBox="0 0 1024 1024">
              <!-- # + iconfont项目内symbol图标代码 -->
              <use xlink:href="#icon-headxiazai" />
            </svg>
          </el-button>
          <el-button type="success" v-if="getPermission('btn:wms:productBase:PackageExport')"
            @click="packageExportHandler">
            商品包装导出
            <svg aria-hidden="true" style="
                width: 1em;
                height: 1em;
                position: relative;
                top: 2px;
                fill: currentColor;
                overflow: hidden;
              " viewBox="0 0 1024 1024">
              <!-- # + iconfont项目内symbol图标代码 -->
              <use xlink:href="#icon-headdaochu" />
            </svg>
          </el-button>
        </el-col>
      </el-row>
      <el-form ref="form" :model="formData" label-width="90px" class="searchform">
        <el-row :gutter="20">
          <el-col :lg="6" :md="6">
            <el-form-item label="商品">
              <el-input v-model="formData.productCode" placeholder="商品名称/编码/助记码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="批准文号">
              <el-input v-model="formData.approvalNumbers"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="同步时间">
              <el-date-picker v-model="dateValue" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="生产厂家">
              <el-input v-model="formData.manufacturer"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="6" :md="6">
            <el-form-item label="ABC分类">
              <el-select v-model="formData.diving" clearable filterable>
                <el-option label="请选择" value="" />
                <el-option v-for="item in ABCFL" :key="item.dictCode" :value="item.dictCode" :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="存放属性">
              <el-select v-model="formData.storageAttribute" clearable filterable>
                <el-option label="请选择" value="" />
                <el-option v-for="item in CFSX" :key="item.dictCode" :value="item.dictCode" :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- <el-card shadow="never" class="table-card">
            <el-row>
                <el-col :span="8">
                    列表信息
                </el-col>
            </el-row>
            <div class="table-container">
                <el-table 
                    :data="tableData"   
                    border
                    height="600"
                    :loading="loading"
                    highlight-current-row
                    @row-dblclick="handleRowDblClick"
                    >
                    <el-table-column label="序号" width="60" align="center">
                        <template slot-scope="{ $index }">
                        {{ $index + 1 }}
                        </template>
</el-table-column>
<el-table-column v-for="item in tableCol" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width"
  show-overflow-tooltip align="center" />
</el-table>
</div>
<div class="block" style="display:flex;justify-content:flex-end;padding:10px 40px 0 0;">
  <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page="paginNation.currentPage" :page-sizes="[100, 200, 300, 400]" :page-size="100"
    layout="total, sizes, prev, pager, next, jumper" :total="paginNation.total">
  </el-pagination>
</div>
</el-card> -->

    <xyy-panel title="列表信息">
      <div slot="tools" style="float:right;">
        <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
      </div>
      <div v-table class="table-box">
        <vxe-Table ref="xTable" :needCheckBox="true" :needSeq="true" :loading="loading" highlight-current-row
          highlight-hover-row height="auto" :data="tableData" :seq-config="{
            startIndex: (paginNation.pageNo - 1) * paginNation.pageSize,
          }" :columns="tableCol" @cell-dblclick="handleRowDblClick" resizable @resizable-change="resizableChange"
          :key="tableKey">
          <!-- <vxe-column type="checkbox" width="60"></vxe-column> -->
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in tableCol">
            <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
              :min-width="item.width">
            </vxe-table-column>
          </template>
        </vxe-Table>
      </div>
      <div class="pager">
        <vxe-pager border :current-page="paginNation.pageNo" :page-size="paginNation.pageSize"
          :total="paginNation.total" :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]" @page-change="handleCurrentChange" />
      </div>
    </xyy-panel>
    <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
    <file-dialog ref="fileDialog" @refresh="getList" />
  </div>
</template>
<script>
import { queryGoodsinfoTableData } from "@/api/basic/index";
import { queryDictBase } from "@/api/basic/public";
import { tableCol } from "./config";
import { exportData } from "../../../api/public";
import utils from "@/utils";
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
import fileDialog from './components/fileDialog.vue';
export default {
  components: { fileDialog },
  data() {
    return {
      loading: false,
      searchLoading: false,
      exportLoading: false,
      formData: {
        approvalNumbers: "",
        productCode: "",
        manufacturer: "",
        diving: "",
        storageAttribute: "",
      },
      ABCFL: [], //ABC分类
      CFSX: [], //存放属性
      dateValue: "",
      paginNation: {
        pageSize: 100, //每页条数
        pageNo: 1, //当前页码
        total: 0, //总条数
        currentPage: 1,
      },
      tableCol: tableCol(),
      oldTableCol: JSON.parse(JSON.stringify(tableCol())), //原始列元素
      tableKey: Date.now(), //表格列刷新flag
      storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
      tableData: [],
    };
  },
  // mounted() {
  //   this.queryDictBase();
  //   this.queryGoodsinfoTableData();
  // },
  activated() {
    this.queryDictBase();
    this.queryGoodsinfoTableData();
    this.getColumWidth('goodsinfo', 'tableCol', 'xTable')
    this.$nextTick(() => {
      utils.pageActivated()
    })

  },
  methods: {
    importRegulatory() {
      this.$refs.fileDialog.open('', '批量维护监管信息')
    },
    importProfitGoods() {
      this.$refs.fileDialog.open('', '导入中包装维护数据')
    },
    packageExportHandler() {
      if (this.tableData.length == 0) {
        this.$message.warning('暂无数据导出')
        return
      }
      const { pageNo, pageSize } = this.paginNation;
      const formInfo = Object.assign(
        { pageNo, pageSize },
        { startTime: this.dateValue ? this.dateValue[0] : "" },
        { endTime: this.dateValue ? this.dateValue[1] : "" },
        this.formData
      );
      const colNameDesc = "商品编码,商品名称,小包装条码,中包装条码,件包装条码,存储条件,商品ABC分类,包装类型,包装数,长,宽,高,重量,体积"
      const colName = "productCode,productName,smallPackageBarCode,mediumPackageBarCode,piecePackageBarCode,storageConditions,divName,packingType,packingNumber,packingLong,packingWide,packingHigh,packingWeightStr,packingVolume"
      const exportParams = JSON.stringify(formInfo)
      const params = {
        moduleName: 'BASICDATA',
        menuDesc: '基础资料-商品包装',
        taskBean: 'ProductPackingExportTask',
        orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
        warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
        colNameDesc: colNameDesc,
        colName: colName,
        exportParams: exportParams
      }
      this.$confirm('是否确认导出表单商品包装？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportData(params).then(res => {
          const { code, msg, result } = res
          if (code === 0 && result) {
            this.$message({
              type: 'success',
              message: '导出成功，请前往下载中心查看！！!'
            })
          } else {
            this.$message.error(msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    },
    // 设置表头筛选列-子组件回传
    setFilterTableHead({ type, fullColumns, tableNo }) {
      this.tableCol = [...fullColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('goodsinfo', 'tableCol')
      this.getColumWidth('goodsinfo', 'tableCol', 'xTable')
    },
    //设置筛选列
    setingTableDataHander(index) {
      this.type = index;
      let columns = [];
      columns = JSON.parse(JSON.stringify((this.tableCol)));
      this.$refs.filterDialog.open(columns, 1, true, 'xTable')
    },
    //重置自定义列宽方法
    //每一个vxe-table单独实现
    resetFilterTableHead(tableNo) {
      this.oldTableCol = JSON.parse(JSON.stringify((tableCol())));
      this.tableCol = [...this.oldColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('goodsinfo', 'oldTableCol')
      this.getColumWidth('goodsinfo', 'tableCol', 'xTable')
    },
    //监测拖动列宽变化方法
    resizableChange({ column }) {
      this.tableCol[this.tableCol.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },
    //查询自定义列方法实现
    // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
    // column:所需要渲染的表格绑定的列数据
    // table: 所需要渲染的表格的 'ref'
    getColumWidth(page, column, table) {
      const params = {
        page: page,
      }
      queryUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          const columns = result
          //出参只含field和width的匹配
          columns.forEach(item => {
            //更改对应column的
            this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
            //匹配后端所传列顺序
            const foundItem = this[column].find(d => d.field === item.field)
            if (foundItem) {
              this[column].push(foundItem)
              this[column].splice(this[column].indexOf(foundItem), 1)
            }
          })
          this.storeDone = true //查询到列表格数据标识
          this.tableKey = Date.now() //强制刷新表格列
          // 若返回数据格式存在 field,title,visible,width
          // this[column] = columns
          // this.tableKey = Date.now() 
          // this.storeDone = true
        } else {
          this.storeDone = false
        }
      })
    },

    //保存/更新自定义列方法实现
    //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
    // column： 所需要保存的表格绑定的列数据
    setColumnWidth(page, column) {
      const columns = this[column]
      const params = {
        page: page,
        columns: columns
      }
      saveUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    },
    searchHandler() {
      this.searchLoading = true;
      this.paginNation.pageNo = 1;
      this.queryGoodsinfoTableData();
    },
    queryDictBase() {
      //字典
      const params = { items: ["ABCFL", "CFSX"] };
      //字典
      queryDictBase(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          (this.ABCFL = (result && result.ABCFL) || []),
            (this.CFSX = (result && result.CFSX) || []);
        }
      });
    },
    queryGoodsinfoTableData() {
      this.loading = true;
      const { pageNo, pageSize } = this.paginNation;
      const params = Object.assign(
        { pageNo, pageSize },
        { startTime: this.dateValue ? this.dateValue[0] : "" },
        { endTime: this.dateValue ? this.dateValue[1] : "" },
        this.formData
      );
      queryGoodsinfoTableData(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.tableData = result.result || [];
          this.paginNation.currentPage = result.pageNo || 1;
          this.paginNation.total = parseInt(result.totalCount);
        } else {
          this.tableData = [];
          this.paginNation.currentPage = 1;
          this.paginNation.total = 0;
          this.$message.error(msg);
        }
        this.loading = false;
        this.searchLoading = false;
      });
    },
    handleSizeChange(val) {
      this.paginNation.pageSize = val;
      this.paginNation.currentPage = val;
      this.queryGoodsinfoTableData();
    },
    handleCurrentChange({ currentPage, pageSize }) {
      this.paginNation.pageNo = currentPage;
      this.paginNation.pageSize = pageSize;
      this.queryGoodsinfoTableData();
    },
    //跳转详情
    handleRowDblClick({ row }) {
      this.$router.push({
        path: "/basic/goodsInfo/edit",
        query: { id: row.id },
      });
    },
    exportHandler() {
      if (!this.tableData.length) {
        this.$message.warning('无数据可供导出！')
        return false
      }
      const { pageNo, pageSize } = this.paginNation;
      const formInfo = Object.assign(
        { pageNo, pageSize },
        { startTime: this.dateValue ? this.dateValue[0] : "" },
        { endTime: this.dateValue ? this.dateValue[1] : "" },
        this.formData
      );
      const colNameDesc = this.tableCol.filter(item => item.visible === true).map(item => item.title).join(',')
      const colName = this.tableCol.filter(item => item.visible === true).map(item => item.field).join(',')
      const exportParams = JSON.stringify(formInfo)
      const params = {
        moduleName: 'BASICDATA',
        menuDesc: '基础资料-商品',
        taskBean: 'ProductBaseExportTask',
        orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
        warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
        colNameDesc: colNameDesc,
        colName: colName,
        exportParams: exportParams
      }
      // console.log(params, 'params');
      this.$confirm('是否确认导出表单内容？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportData(params).then(res => {
          const { code, msg, result } = res
          if (code === 0 && result) {
            this.$message({
              type: 'success',
              message: '导出成功，请前往下载中心查看！！!'
            })
          } else {
            this.$message.error(msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    },
  },
};
</script>
<style scoped>
.form-card .side-right {
  display: flex;
  justify-content: flex-end;
}

.table-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-box {
  height: 440px !important;
}
</style>
