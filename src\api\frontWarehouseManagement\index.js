import request from "@/utils/request";

//前置仓-销售订单管理
export function getFrontWarehouseOrderList(data) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/list",
    method: "post",
    data,
  });
}

//前置仓-拣货明细查询
export function getFrontWarehouseOrderDetail(data) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/queryPickingDetails",
    method: "post",
    data,
  });
}

//前置仓-拣货任务管理
export function getFrontWarehouseOrderTask(data) {
  return request({
    url: "/outstock/web/outstock/packtask/frontList",
    method: "post",
    data,
  });
}

//重打拣货单
export function getFrontWarehouseOrderReprint(data) {
  return request({
    url: "/outstock/web/outstock/frontWarehousePicking/reprintPickPrint",
    method: "post",
    data,
  });
}