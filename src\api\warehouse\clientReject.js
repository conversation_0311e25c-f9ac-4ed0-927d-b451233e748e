import request from '@/utils/request'

/**
 * 获取驳回列表
 * @param {data} 请求体
 */
export function findCommissionList(data) {
  return request({
    url: '/instock/purchase/commission/findCommissionList',
    method: 'post',
    data: data
  })
}

/**
 * 提交
 * @param {data} 请求体
 */
export function doSubmit(data) {
  return request({
    url: 'instock/purchase/commission/doSubmit',
    method: 'post',
    data: data
  })
}

/**
 * 导出
 * @param {data} 请求体
 */
export function commissionListExport(data) {
  return request({
    url: '/purchase/commission/commissionListExport',
    method: 'post',
    data: data
  })
}
