<template>
  <xyy-dialog ref="dialogTableVisible" :title="title" width="500" height="220">
    <div style="display: flex; justify-content: space-around; width: 100%">
      <div style="display: flex; flex-direction: column" @click="download">
        <div><span style="color: red">*</span><span>模版下载</span></div>
        <div class="xiazai">
          <!-- <i class="el-icon-download" style="font-size: 100px"></i> -->
          <svg aria-hidden="true" style="width: 90px;height:90px;margin-top:30px;" viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headwenjianxiazai" />
          </svg>
          <div>下载模版</div>
        </div>
      </div>
      <div style="display: flex; flex-direction: column;">
        <div><span style="color: red">*</span><span>上传文件</span></div>
        <el-upload class="upload-demo" drag ref="upload" action="#" :file-list="fileList" :before-upload="beforeUpload"
          :on-change="onChange" :on-success="handleFileSuccess" :on-error="handleFileError" accept=".xls,.xlsx"
          :auto-upload="false" :http-request="uploadFile">
          <svg aria-hidden="true" style="width: 90px;height: 90px;margin-top:30px;">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headwenjianshangchuan" />
          </svg>
          <div class="el-upload__text">上传</div>
          <div class="el-upload__tip" slot="tip">仅支持xls，xlsx，小于10M</div>
        </el-upload>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitUpload()">提 交</el-button>
    </span>
  </xyy-dialog>
</template>

<script>
import utils from "@/utils";
import {
  uploadWrapFile
} from '@/api/basic/index'
export default {
  name: "FileDialog",
  data() {
    return {
      submitLoading: false,
      title: "",
      show: false,
      listType: ["xls", "xlsx"],
      fileList: [],
      formData: {
      }
    };
  },
  methods: {
    /**
  * 文件上传成功后的处理方法。
  * @param {Object} response - 上传文件后服务器的响应。
  * @param {Object} file - 上传的文件对象。
  * @param {Array} fileList - 上传的文件列表。
  * @returns {void}
  */
    handleFileSuccess(response, file, fileList) {
      const { code, msg, result } = response;
      // 如果返回的 code 为 1，表示上传失败，显示错误消息
      if (code == 1) {
        this.$message({
          message: msg,
          type: "error",
        });
      } else if (code == 0) {
        // 如果返回的 code 为 0，表示上传成功，显示成功消息
        // console.log(response, "mybaby2"); // 调试信息
        this.$message({
          message: msg,
          type: "success",
        });
      }
      // console.log(response, "mybaby1"); // 调试信息
    },

    /**
     * 文件上传错误后的处理方法。
     * @param {Object} response - 上传文件后服务器的响应。
     * @param {Object} file - 上传的文件对象。
     * @param {Array} fileList - 上传的文件列表。
     * @returns {void}
     */
    handleFileError(response, file, fileList) {
      // 显示上传文件失败的错误消息
      this.$message({
        message: "上传文件失败",
        type: "error",
      });
      // 将提交加载状态设置为 false
      this.submitLoading = false;
    },

    /**
     * 下载模版文件的方法。
     * @returns {void}
     */
    download() {
      // 调用工具方法打开下载对话框，传入模板文件的URL
      if (this.title == '导入中包装维护数据') {
        utils.openDownloadDialog(
          "https://fastdfs-prod-1300196397.cos.ap-beijing.myqcloud.com/B2BCOS/WmsCloud/template/%E5%95%86%E5%93%81%E4%B8%AD%E5%8C%85%E8%A3%85%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E6%A8%A1%E6%9D%BF.xlsx",
          "中包装维护模板"
        );
      }
    },

    /**
     * 提交上传文件的方法。
     * @returns {void}
     */
    submitUpload() {
      // 调用上传组件的提交方法
      this.$refs.upload.submit();
    },

    /**
     * 文件上传处理的方法。
     * @param {Object} param - 包含上传文件的参数对象。
     * @returns {void}
     */
    uploadFile(param) {
      this.submitLoading = true; // 设置提交加载状态为 true
      // console.log("param", param); // 调试信息
      const formData = new FormData();
      formData.append("file", param.file); // 将文件追加到表单数据中
      // 如果未选择文件，显示错误信息并重置加载状态
      if (formData.get("file") == null) {
        this.$message.error("请选择文件");
        this.submitLoading = false;
        return;
      }
      // 调用导入溢出文件的方法
      if (this.title == '导入中包装维护数据') {
        uploadWrapFile(formData).then((res) => {
          const { code, msg, result } = res;
          // 根据返回的 code 判断上传结果
          if (code === 1) {
            // 上传失败，显示错误消息
            this.$message({
              message: msg,
              type: "error",
            });
            this.submitLoading = false;
            // 打开下载对话框，下载错误文件
            // utils.openDownloadDialog(result,"中包装导入失败信息.xlsx");
          } else if (code === 0) {
            // 上传成功，显示成功消息
            this.$message({
              message: msg,
              type: "success",
            });
            this.submitLoading = false;
            this.$emit("refresh"); // 触发刷新事件
            this.close(); // 关闭上传对话框
          } else {
            // 处理其他返回结果，显示错误消息
            this.$message({
              message: msg,
              type: "error",
            });
            this.submitLoading = false;
            // 打开下载对话框，下载错误文件
            // utils.openDownloadDialog(result,"中包装导入失败信息.xlsx");
          }
        });
      }
    },

    /**
     * 文件列表变化时的处理方法，确保文件列表只包含一个文件。
     * @param {Object} file - 上传的文件对象。
     * @param {Array} fileList - 上传的文件列表。
     * @returns {void}
     */
    onChange(file, fileList) {
      // 如果文件列表长度大于1，删除第一个文件
      // console.log(fileList); // 调试信息
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
    },

    /**
  * 在上传文件之前进行检查的方法。
  * @param {Object} file - 上传的文件对象。
  * @returns {boolean} 如果文件符合条件，则返回 true；否则返回 false。
  */
    beforeUpload(file) {
      // 获取文件扩展名
      let testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      // 检查文件类型是否在允许的类型列表中
      let flag = this.listType.includes(testmsg);
      // 如果文件类型不符合，显示警告信息并返回 false
      if (!flag) {
        this.$message({
          message: "上传文件只能是 xls、xlsx格式!",
          type: "warning",
        });
        return false;
      }
      // 检查文件大小是否超过10M
      if (file.size > 10 * 1024 * 1024) {
        this.$message({
          message: "上传文件不能超过10M",
          type: "warning",
        });
        return false;
      }
      // 如果文件符合所有条件，返回 true
      return true;
    },

    /**
   * 打开弹窗并初始化数据的方法。
   * @param {Object} data - 要传递给弹窗的表单数据。
   * @returns {void}
   */
    open(data, title) {
      // 清空文件列表
      this.fileList = [];
      // 重置表单数据
      this.formData = {};
      // 设置弹窗标题
      this.title = title;
      // 显示弹窗
      this.show = true;
      // 将传递的数据赋值给表单数据
      this.formData = data;
      // 打开弹窗的引用组件
      this.$refs.dialogTableVisible.open();
    },

    /**
     * 关闭弹窗并重置数据的方法。
     * @returns {void}
     */
    close() {
      // 关闭弹窗的引用组件
      this.$refs.dialogTableVisible.close();
      // 清空文件列表
      this.fileList = [];
      // 隐藏弹窗
      this.show = false;
    },

    /**
     * 处理弹窗关闭事件的方法。
     * @returns {void}
     */
    handleClose() {
      // 触发 "on-before-close" 事件
      this.$emit("on-before-close");
    },

  },
};
</script>

<style scoped>
/* 定义一个类名为 xiazai 的样式 */
.xiazai {
  /* 设置背景颜色为白色 */
  background-color: #fff;
  /* 设置边框为虚线，颜色为 #d9d9d9 */
  border: 1px dashed #d9d9d9;
  /* 设置边框圆角为 6px */
  border-radius: 6px;
  /* 设置元素宽度为 300px */
  width: 300px;
  /* 设置元素高度为 180px */
  height: 180px;
  /* 设置盒模型为 border-box，使 padding 和 border 包含在 width 和 height 内 */
  box-sizing: border-box;
  /* 设置文本对齐方式为居中 */
  text-align: center;
  /* 设置鼠标指针为手型，表示可点击 */
  cursor: pointer;
  /* 设置元素的定位为相对定位 */
  position: relative;
  /* 设置元素的溢出部分为隐藏 */
  overflow: hidden;
}
</style>
